import App from './App'
import store from '@/store/index.js' 

import Router from 'vue-router'

Vue.use(Router)

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App,store
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp },Vue from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app,store
  }

}
// #endif
