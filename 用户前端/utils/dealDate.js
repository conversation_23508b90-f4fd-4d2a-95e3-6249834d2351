export function startDemo(zkxList) {
	let record = []
	for (let zkx = 0; zkx < zkxList.length; zkx++) {
		for (let i = 0; i < zkxList[zkx].productLists.length; i++) {


			if (zkxList[zkx].productLists[i].code == "9900003973") {
				zkxList[zkx].productLists[i].parms = {
					customization: {
						"items": [],
						"options": []
					},
					comboItems: []
				}
				continue;
			}
			let comboItems = []
			let customization = {
				items: [],
				options: []
			}
			//type类型等于1的话，并且定制，就证明
			if (zkxList[zkx].productLists[i].type == 1 && zkxList[zkx].productLists[i].customization) {
				let discountArrCustomization = zkxList[zkx].productLists[i].customization
				discountArrCustomization.options.length > 0 ? discountArrCustomization = discountArrCustomization.options :
					discountArrCustomization = discountArrCustomization.items
				if (discountArrCustomization.length == 1) {
					//单选
					let opobj = {
						code: discountArrCustomization[0].code,
						values: discountArrCustomization[0].values
					}
					customization.items.push(opobj)
				} else {
					//多选
					for (let keyi = 0; keyi < discountArrCustomization.length; keyi++) {
						if (discountArrCustomization[keyi].checked == 0) {
							let keyiobj = {
								checked: discountArrCustomization[keyi].checked,
								code: discountArrCustomization[keyi].code
							}
							customization.options.push(keyiobj)
						}
					}
				}
			}
			let objs = {
				customization: customization,
				comboItems: null
			}
			if (zkxList[zkx].productLists[i].type == 2 || zkxList[zkx].productLists[i].type == 7) {
				if (zkxList[zkx].productLists[i].comboItems) {
					for (let y = 0; y < zkxList[zkx].productLists[i].comboItems.length; y++) {
						for (let k = 0; k < zkxList[zkx].productLists[i].comboItems[y].comboProducts.length; k++) {
							if (zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].isDefault == 1) {
								let obj = {
									"code": zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].code,
									"hasCustomization": 0,
								}
								if (zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].hasCustomization) {
									if (zkxList[zkx].productLists[i].comboItems[y].className == "饮料" || zkxList[zkx].productLists[i]
										.comboItems[y].className == "小食") {
										obj.hasCustomization = 1;
										obj.items = []
										if (zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization && zkxList[zkx]
											.productLists[i].comboItems[y].comboProducts[k].customization.items) {
											if(zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization
													.items[0] || zkxList[zkx].productLists[i].longName !== "随心配1+1超值套餐"){
												for (let b = 0; b < zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization
													.items[0].values.length; b++) {
													if (zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization.items[0].values[b]
														.checked == 1) {
														obj.items.push({
															"code": zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization.items[
																0].code,
															"values": [{
																"code": zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization
																	.items[0].values[b].code,
																"checked": zkxList[zkx].productLists[i].comboItems[y].comboProducts[k]
																	.customization.items[0].values[b].checked
															}]
														})
													}
												}
											}
										} else {
											obj.hasCustomization = 0
										}
									} else {
										obj.hasCustomization = 1;
										obj.options = []
										if (zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization) {
											for (let b = 0; b < zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization
												.options.length; b++) {
												if (zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization.options[b]
													.checked == 0) {
													obj.options.push({
														code: zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization.options[
															b].code,
														checked: zkxList[zkx].productLists[i].comboItems[y].comboProducts[k].customization
															.options[b].checked
													})
												}
											}
										}
										if (obj.options.length == 0) {
											obj.hasCustomization = 0
										}
									}
								} else {
									obj.options = []
								}
								comboItems.push(obj)
							}
						}
					}
				}
				//会员定制


			}

			objs.comboItems = comboItems
			console.log(objs)
			zkxList[zkx].productLists[i].parms = objs
			record.push(objs)


		}
	}
	return zkxList
}