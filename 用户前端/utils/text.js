[{
	"beCode": "",
	"beType": "1",
	"carLicensePlate": "",
	"categoryId": 1,
	"categoryName": "到店取餐",
	"channel": "03",
	"couponList": [],
	"createTime": "2024-04-12 19:25:09",
	"customerServicePhone": "4009-200-205",
	"customerServiceUrl": "https://mcd.s7.udesk.cn/im_client/?web_plugin_id=9&nonce=49d635cf0ff34a19951b67cea91dcd34&timestamp=1712921122435&web_token=MEDDY171167796362617183&customer_token=MEDDY171167796362617183&encryption_algorithm=SHA256&signature=68ADF253E3D8D624A8E280A5589FD31CF149EBC0162A953956026D6D55FFBB14&v_nonce=49d635cf0ff34a19951b67cea91dcd34&v_timestamp=1712921122435&v_signature=B3E1CE31EBFA5641682DD01DF1437AA4B2EAFB64&v=6.0.39.1&c_cf_dialogueDesc=CMA,FC,pickup,1030210520000365519668835625&sid=26a2df9da5e54c146a07bce168179141_&c_cf_sid=26a2df9da5e54c146a07bce168179141_",
	"daypartCode": 5,
	"deliveryPrice": "0",
	"eatType": 2,
	"eatTypeName": "外带",
	"expectPickUpTime": "2024-04-12 19:30:16",
	"invoice": {
		"code": 1,
		"desc": "订单完成后可开具电子发票\r\n开票金额为消费者实付款金额，红包、优惠、现金券、礼品卡、麦钱包余额，购买寄存券金额不在开票范围内。如订单发生退款，开票金额将变更为最终实付款金额。"
	},
	"mpOrderStatusCode": "33",
	"orderActionList": [{
		"code": "another_one",
		"name": "再来一单"
	}, {
		"code": "customer_service",
		"name": "联系客服"
	}, {
		"code": "cancel",
		"name": "取消订单"
	}, {
		"code": "advise",
		"name": "投诉建议",
		"url": "mcdapp://page?iosPageName=MCDReactNativeViewController&androidPageName=com.mcd.library.rn.McdReactNativeActivity&parameters={"
		rctModuleName ":"
		suggestDetail ","
		rctModuleParams ":{"
		orderStoreName ":"
		麦当劳福州群升餐厅 ","
		questionId ":0,"
		question ":"
		到店取餐 "},"
		rctModule ":"
		mcduser "}"
	}],
	"orderFlag": 1,
	"orderId": "1030210520000365519668835625",
	"orderMode": "0",
	"orderProcessNodeHistory": [{
		"isCurrent": 0,
		"processNode": "10",
		"processNodeName": "待配餐"
	}, {
		"isCurrent": 1,
		"processNode": "20",
		"processNodeName": "配餐中"
	}, {
		"isCurrent": 0,
		"processNode": "31",
		"processNodeName": "待取餐"
	}, {
		"isCurrent": 0,
		"processNode": "41",
		"processNodeName": "已完成"
	}],
	"orderProductList": [{
		"comboItemList": [{
			"name": "嘎嘣脆贡菜卷",
			"productCode": "520311",
			"quantity": 1
		}, {
			"name": "迷你朱古力新地",
			"productCode": "4422",
			"quantity": 1
		}],
		"price": "13.9",
		"productCode": "9900000883",
		"productImage": "https://menu-img.mcd.cn/pcm/prod/menu/20240406a5/product/MS_9900000883_320.png",
		"productName": "随心配1+1超值套餐",
		"productType": "7",
		"quantity": 1,
		"realSubtotal": "13.9",
		"sequence": 2,
		"subtotal": "13.9",
		"tags": []
	}],
	"orderStatus": "配餐中",
	"orderStatusCode": "10",
	"orderStatusSubTitle": "餐厅配餐中，冰淇淋在取餐时现场制作...",
	"orderStatusSubTitleIcon": "https://img.mcd.cn/gallery/7b45cd732ac9fb1e.png",
	"orderStatusTitle": "35442",
	"orderStatusTitleType": 1,
	"orderType": "1",
	"orderTypeName": "到店取餐",
	"payId": "11698977024748937216",
	"paymentChannel": "ALI",
	"paymentChannelLabel": "支付宝",
	"paymentTransactionId": "2024041222001479911418757820",
	"pickupCode": "35442",
	"pickupTips": "",
	"pickupWaitMinute": 0,
	"pickupWaitOrder": 0,
	"productTotalPrice": "13.9",
	"realDeliveryPrice": "0",
	"realPayAmount": "13.9",
	"realProductTotalPrice": "13.9",
	"realTotalAmount": "13.9",
	"remark": "",
	"saleTime": "2024-04-12 19:25:16",
	"saleType": "1",
	"storeAddress": "八一七中路888号群升国际AD块1号楼转角处",
	"storeCityCode": "350100",
	"storeCityName": "福州市",
	"storeCode": "1410048",
	"storeLatitude": "26.055636",
	"storeLongitude": "119.308069",
	"storeName": "麦当劳福州群升餐厅",
	"tablewareInfo": "不需要餐具",
	"topBgImg": "https://img.mcd.cn/gallery/278672409695ffb6.png",
	"totalAmount": "13.9",
	"totalDiscountAmount": "0"
}, {
	"beCode": "",
	"beType": "1",
	"carLicensePlate": "",
	"categoryId": 1,
	"categoryName": "到店取餐",
	"channel": "03",
	"couponList": [],
	"createTime": "2024-04-12 19:25:11",
	"customerServicePhone": "4009-200-205",
	"customerServiceUrl": "https://mcd.s7.udesk.cn/im_client/?web_plugin_id=9&nonce=abfbc73c33cd431ea33ba98c93bc1059&timestamp=1712921133228&web_token=MEDDY182289243673057193&customer_token=MEDDY182289243673057193&encryption_algorithm=SHA256&signature=EC669AEFAF5E26C9068607E7F91C27DE82044DB7FD9B7E6D2D94E3AB59F6D04F&v_nonce=abfbc73c33cd431ea33ba98c93bc1059&v_timestamp=1712921133228&v_signature=8A1FCBF94FA98F3776E513A6E0FA32ED3F5EA2FE&v=6.0.39.1&c_cf_dialogueDesc=CMA,FC,pickup,1030549700000365519987599120&sid=6e72697917c03ce006cc5a9527967f4a_&c_cf_sid=6e72697917c03ce006cc5a9527967f4a_",
	"daypartCode": 5,
	"deliveryPrice": "0",
	"eatType": 2,
	"eatTypeName": "外带",
	"expectPickUpTime": "2024-04-12 19:30:25",
	"invoice": {
		"code": 1,
		"desc": "订单完成后可开具电子发票\r\n开票金额为消费者实付款金额，红包、优惠、现金券、礼品卡、麦钱包余额，购买寄存券金额不在开票范围内。如订单发生退款，开票金额将变更为最终实付款金额。"
	},
	"mpOrderStatusCode": "33",
	"orderActionList": [{
		"code": "another_one",
		"name": "再来一单"
	}, {
		"code": "customer_service",
		"name": "联系客服"
	}, {
		"code": "cancel",
		"name": "取消订单"
	}, {
		"code": "advise",
		"name": "投诉建议",
		"url": "mcdapp://page?iosPageName=MCDReactNativeViewController&androidPageName=com.mcd.library.rn.McdReactNativeActivity&parameters={"
		rctModuleName ":"
		suggestDetail ","
		rctModuleParams ":{"
		orderStoreName ":"
		麦当劳福州群升餐厅 ","
		questionId ":0,"
		question ":"
		到店取餐 "},"
		rctModule ":"
		mcduser "}"
	}],
	"orderFlag": 1,
	"orderId": "1030549700000365519987599120",
	"orderMode": "0",
	"orderProcessNodeHistory": [{
		"isCurrent": 0,
		"processNode": "10",
		"processNodeName": "待配餐"
	}, {
		"isCurrent": 1,
		"processNode": "20",
		"processNodeName": "配餐中"
	}, {
		"isCurrent": 0,
		"processNode": "31",
		"processNodeName": "待取餐"
	}, {
		"isCurrent": 0,
		"processNode": "41",
		"processNodeName": "已完成"
	}],
	"orderProductList": [{
		"comboItemList": [{
			"name": "汉堡包",
			"productCode": "1000",
			"quantity": 1
		}, {
			"name": "迷你薯条",
			"productCode": "504643",
			"quantity": 1
		}, {
			"name": "开心乐园餐小玉米",
			"productCode": "505643",
			"quantity": 1
		}, {
			"name": "纯牛奶(盒装)",
			"productCode": "6461",
			"quantity": 1
		}, {
			"name": "随机玩具1个",
			"productCode": "7001",
			"quantity": 1
		}],
		"price": "22",
		"productCode": "504640",
		"productImage": "https://menu-img.mcd.cn/pcm/prod/menu/20240406a5/product/MS_504640_320.png",
		"productName": "汉堡开心乐园餐",
		"productType": "2",
		"quantity": 1,
		"realSubtotal": "22",
		"sequence": 2,
		"subtotal": "22",
		"tags": []
	}],
	"orderStatus": "配餐中",
	"orderStatusCode": "10",
	"orderStatusSubTitle": "餐厅配餐中，冰淇淋在取餐时现场制作...",
	"orderStatusSubTitleIcon": "https://img.mcd.cn/gallery/7b45cd732ac9fb1e.png",
	"orderStatusTitle": "35443",
	"orderStatusTitleType": 1,
	"orderType": "1",
	"orderTypeName": "到店取餐",
	"payId": "11698977034735616000",
	"paymentChannel": "ALI",
	"paymentChannelLabel": "支付宝",
	"paymentTransactionId": "2024041222001479911421395116",
	"pickupCode": "35443",
	"pickupTips": "",
	"pickupWaitMinute": 0,
	"pickupWaitOrder": 0,
	"productTotalPrice": "22",
	"realDeliveryPrice": "0",
	"realPayAmount": "22",
	"realProductTotalPrice": "22",
	"realTotalAmount": "22",
	"remark": "",
	"saleTime": "2024-04-12 19:25:25",
	"saleType": "1",
	"storeAddress": "八一七中路888号群升国际AD块1号楼转角处",
	"storeCityCode": "350100",
	"storeCityName": "福州市",
	"storeCode": "1410048",
	"storeLatitude": "26.055636",
	"storeLongitude": "119.308069",
	"storeName": "麦当劳福州群升餐厅",
	"tablewareInfo": "不需要餐具",
	"topBgImg": "https://img.mcd.cn/gallery/278672409695ffb6.png",
	"totalAmount": "22",
	"totalDiscountAmount": "0"
}]