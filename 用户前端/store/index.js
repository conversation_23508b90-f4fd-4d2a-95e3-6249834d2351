import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate'

Vue.use(Vuex) 
export default new Vuex.Store({
    state: { 
				// 选择城市
        selectedCity:{},
				// 经纬度
				paramsLatLon:{
					latitude:'',
					longitude:''
				},
				// 兑换码id
				codeId:'',
				// 所有套餐详细信息
				packageAllList:[],
				// 门店code
				storeCode:{},
				haveCountParams:{},
				popUpList:[],
				// 兑换参数
				exchangeParams:[],
				proLists:[],
				mockList:[],
				packageParams:[]
    },
    getters:{ 
        
    },
    mutations:{ 
			updatepackageParams(state,newVal){
				state.packageParams=newVal
			},
       updateSelectedCity(state,newVal){
				 state.selectedCity=newVal
			 },
			 updateParamsLatLon(state,newVal){
				 state.paramsLatLon=newVal
			 },
			 updateCodeId(state,newVal){
				 state.codeId=newVal
			 },
			 updatePackageAllList(state,newVal){
				 state.packageAllList = newVal.map((proItem) => {
				     if (proItem.type === 7 && proItem.hasOwnProperty('comboItems')==true) {
				       proItem.comboItems.forEach((comItem) => {
				         const arr = comItem.comboProducts.findIndex((item) => item.isDefault === 1);
				         if(arr!==undefined){
									 comItem.comboProducts.forEach((combItem, index) => {
									   let num = 0;
									   if (combItem.isDefault === 1) {
									     num++;
									     if (comItem.quantity !== num) {
											 console.log(arr)
									       comItem.comboProducts[arr].isDefault = 1;
									     }
									   }
									 });
								 }
				       });
				     }
					 return proItem
				 });
					localStorage.setItem('data',JSON.stringify(state.packageAllList))
			 },
			 updateStoreCode(state,newVal){
				 state.storeCode=newVal
			 },
			 updatehaveCountParams(state,newVal){
				 state.haveCountParams=newVal
			 },
			 updatepopUpList(state,newVal){
				 state.popUpList=newVal
			 },
			 updatExchangeParams(state,newVal){
				 state.exchangeParams=newVal
			 },
			 updateProLists(state,newVal){
				 state.proLists=newVal
			 },
			 updatemockList(state,newVal){
				 state.mockList=newVal
			 }
    },
    actions:{
       
    },
		 plugins: [createPersistedState({
		    storage: window.sessionStorage, // 或者 localStorage
		  })]
})