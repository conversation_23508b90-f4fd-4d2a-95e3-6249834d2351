<script>
	export default {
		onLaunch: function() {

		},
		onShow: function() {
			// const currentRoute = this.$route.meta;
			// const routePath=this.$route.path.replace('/','')
			// this.$store.commit('updateCodeId',routePath)
			// if (routePath && currentRoute.name == "/") {
			// 	uni.redirectTo({
			// 		url: 'cres'
			// 	});
			// }
			// console.log(currentRoute,'currentRoute');
		},
		onLoad() {
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 200
			})
		},
		onHide: function() {
			// sessionStorage.clear();
			// localStorage.clear();
		},
	}
</script>

<style>
	/*每个页面公共css */
</style>