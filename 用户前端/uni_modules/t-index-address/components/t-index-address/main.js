const fs = require('fs');
const axios = require('axios');

// 本地数据存储目录
const DATA_DIR = './data';
// 目标API接口
const TARGET_API = 'http://server.pqkap.com/store/cities/group';

async function main() {
  try {
    // 确保数据目录存在
    await fs.promises.mkdir(DATA_DIR, { recursive: true });

    // 获取目标API数据
    const response = await axios.get(TARGET_API, { timeout: 15000 });
    const targetData = response.data;
    
    // 直接转换数据格式
    const result = targetData.data.groups.map(group => ({
      initial: group.initial,
      list: group.cities.map(city => ({
        code: city.code,
        name: city.name,
        lat: city.latitude || '',
        lng: city.longitude || ''
      }))
    }));
    
    // 保存结果
    await fs.promises.writeFile('./cities.json', JSON.stringify(result, null, 2));
    console.log(`✅ 城市数据已保存（共 ${result.reduce((acc, cur) => acc + cur.list.length, 0)} 条数据）`);

  } catch (error) {
    console.error('❌ 处理失败:', error.message);
    process.exit(1);
  }
}

// 启动
main();