{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path" : "cres",
			"style" : 
			{
				"navigationBarTitleText" : "选择餐厅",
				"enablePullDownRefresh" : false,
				 "onReachBottomDistance": 50
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页"
			}
		},
		{
			"path" : "pages/cres/selectedCity",
			"style" : 
			{
				"navigationBarTitleText" : "选择城市",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/selectProduct/selectProduct",
			"style" : 
			{
				"navigationBarTitleText" : "选择商品",
				"enablePullDownRefresh" : false
			}
		},		{
			"path" : "pages/selectProduct/selectProductDetail",
			"style" : 
			{
				"navigationBarTitleText" : "选择商品",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/pickingMeals/pickingMeals",
			"style" : 
			{
				"navigationBarTitleText" : "柜台取餐",
				"enablePullDownRefresh" : false
				// "navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/mealCode/mealCode",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/logViewer/logViewer",
			"style" : 
			{
				"navigationBarTitleText" : "日志查看器",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/logCompare/logCompare",
			"style" : 
			{
				"navigationBarTitleText" : "日志比对工具",
				"enablePullDownRefresh" : false
			}
		}
	],
	"globalStyle": {
		"navigationStyle": "custom",
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"usingComponents": {
		      "van-action-sheet": "/wxcomponents/vant/action-sheet/index",
		      "van-area": "/wxcomponents/vant/area/index",
		      "van-button": "/wxcomponents/vant/button/index",
		      "van-card": "/wxcomponents/vant/card/index",
		      "van-cell": "/wxcomponents/vant/cell/index",
		      "van-cell-group": "/wxcomponents/vant/cell-group/index",
		      "van-checkbox": "/wxcomponents/vant/checkbox/index",
		      "van-checkbox-group": "/wxcomponents/vant/checkbox-group/index",
		      "van-col": "/wxcomponents/vant/col/index",
		      "van-dialog": "/wxcomponents/vant/dialog/index",
		      "van-field": "/wxcomponents/vant/field/index",
		      "van-goods-action": "/wxcomponents/vant/goods-action/index",
		      "van-goods-action-icon": "/wxcomponents/vant/goods-action-icon/index",
		      "van-goods-action-button": "/wxcomponents/vant/goods-action-button/index",
		      "van-icon": "/wxcomponents/vant/icon/index",
		      "van-loading": "/wxcomponents/vant/loading/index",
		      "van-nav-bar": "/wxcomponents/vant/nav-bar/index",
		      "van-notice-bar": "/wxcomponents/vant/notice-bar/index",
		      "van-notify": "/wxcomponents/vant/notify/index",
		      "van-panel": "/wxcomponents/vant/panel/index",
		      "van-popup": "/wxcomponents/vant/popup/index",
		      "van-progress": "/wxcomponents/vant/progress/index",
		      "van-radio": "/wxcomponents/vant/radio/index",
		      "van-radio-group": "/wxcomponents/vant/radio-group/index",
		      "van-row": "/wxcomponents/vant/row/index",
		      "van-search": "/wxcomponents/vant/search/index",
		      "van-slider": "/wxcomponents/vant/slider/index",
		      "van-stepper": "/wxcomponents/vant/stepper/index",
		      "van-steps": "/wxcomponents/vant/steps/index",
		      "van-submit-bar": "/wxcomponents/vant/submit-bar/index",
		      "van-swipe-cell": "/wxcomponents/vant/swipe-cell/index",
		      "van-switch": "/wxcomponents/vant/switch/index",
		      "van-tab": "/wxcomponents/vant/tab/index",
		      "van-tabs": "/wxcomponents/vant/tabs/index",
		      "van-tabbar": "/wxcomponents/vant/tabbar/index",
		      "van-tabbar-item": "/wxcomponents/vant/tabbar-item/index",
		      "van-tag": "/wxcomponents/vant/tag/index",
		      "van-toast": "/wxcomponents/vant/toast/index",
		      "van-transition": "/wxcomponents/vant/transition/index",
		      "van-tree-select": "/wxcomponents/vant/tree-select/index",
		      "van-datetime-picker": "/wxcomponents/vant/datetime-picker/index",
		      "van-rate": "/wxcomponents/vant/rate/index",
		      "van-collapse": "/wxcomponents/vant/collapse/index",
		      "van-collapse-item": "/wxcomponents/vant/collapse-item/index",
		      "van-picker": "/wxcomponents/vant/picker/index"
		    }
	},
	"uniIdRouter": {}
}
