import {
	checkCode
} from '../api/request.js'
let checkCodeStatus = {
	data() {
		return {

		}
	},
	mounted() {},
	onShow() {
		this.checkCode()
	},
	methods: {
		async checkCode() {
			let params={
				code:this.code
			}
			let res = await checkCode(params)
			//201已使用订单未过期
			if ( res.code == 201) {
				uni.navigateTo({
					url: '/pages/pickingMeals/pickingMeals?s='+this.code
				})
			}else if(res.code!==200){
				uni.navigateTo({
					url:'/cres?s='+this.code
				})
			}
		}
	},
	computed: {

	}
}
export default checkCodeStatus