# 套餐提交数据一致性调试日志系统

## 概述

为了调试套餐提交数据与抓包数据不一致的问题，在应用中添加了详细的日志记录系统。该调试工具可以：

1. **完整数据流追踪**：记录从商品选择到最终提交的完整数据流程
2. **实时调试输出**：在控制台实时查看数据处理过程
3. **数据差异分析**：将应用提交的数据与抓包数据进行详细比较
4. **问题根因定位**：快速找到数据差异的具体位置和原因

## 调试功能

### 1. 自动日志记录

系统在以下关键节点自动记录调试数据：

- **HTTP请求拦截**：所有API请求和响应的完整数据
- **商品选择验证**：商品详情页面的选择验证逻辑
- **参数组装过程**：取餐页面的数据组装和合并逻辑
- **兑换提交流程**：最终的兑换请求和响应处理
- **异常捕获**：所有错误和异常的详细堆栈信息

### 2. 调试数据存储

- 使用localStorage存储调试数据
- 自动限制各类日志数量（防止内存溢出）
- 支持手动清空和JSON导出

### 3. 调试日志查看器

路径：`/pages/logViewer/logViewer`

- 按类型分类查看调试日志
- JSON格式化显示详细数据
- 批量导出调试数据
- 清空历史调试记录

### 4. 数据差异比对工具

路径：`/pages/logCompare/logCompare`

- 输入抓包工具获取的JSON数据
- 选择对应的应用提交日志
- 递归深度比较数据结构差异
- 详细显示差异路径和值
- 导出比对分析结果

## 调试流程

### 1. 问题重现

1. 按正常流程操作应用
2. 完成商品选择和兑换流程
3. 系统自动记录所有关键调试数据

### 2. 调试日志分析

1. 访问 `/pages/logViewer/logViewer`
2. 按类型查看调试日志（API请求、参数组装、兑换结果等）
3. 展开查看详细的JSON数据结构
4. 导出调试数据用于进一步分析

### 3. 抓包数据获取

1. 使用抓包工具（Charles、Fiddler、Wireshark等）
2. 捕获实际的HTTP请求数据
3. 提取请求体的JSON数据

### 4. 数据差异分析

1. 访问 `/pages/logCompare/logCompare`
2. 粘贴抓包获取的JSON数据
3. 选择对应的应用提交日志记录
4. 执行深度比对分析
5. 查看详细的差异报告

### 5. 差异定位

比对工具输出：
- **数据路径**：`foodData[0].POSITIVE[0].comboItems[0].quantity`
- **抓包值**：实际网络请求中的值
- **应用值**：应用内部提交的值
- **差异类型**：值不匹配、类型不匹配、字段缺失等

## 调试日志类型

### API请求日志 (api_logs)

记录所有HTTP请求的详细信息：
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "type": "REQUEST", // REQUEST, RESPONSE, ERROR
  "url": "/addCashCouponOrder",
  "method": "POST",
  "data": { /* 请求/响应数据 */ }
}
```

### 商品详情日志 (product_detail_logs)

记录商品选择验证过程：
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "type": "PRODUCT_SUBMIT_VALIDATION",
  "packageAllList": [ /* 商品列表数据 */ ],
  "validationCount": [ /* 验证结果 */ ],
  "code": "兑换码"
}
```

### 参数组装日志 (param_arrange_logs)

记录数据组装过程：
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "type": "PARAM_ARRANGEMENT",
  "originalPackageParams": [ /* 原始参数 */ ],
  "originalPackageAllList": [ /* 原始商品列表 */ ],
  "finalTrParams": { /* 最终提交参数 */ }
}
```

### 兑换结果日志 (exchange_logs)

记录兑换请求和响应：
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "type": "EXCHANGE_RESULT",
  "requestParams": { /* 请求参数 */ },
  "response": { /* 响应数据 */ },
  "resultCode": 200
}
```

### 错误日志 (exchange_error_logs)

记录异常和错误：
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "type": "EXCHANGE_ERROR",
  "requestParams": { /* 请求参数 */ },
  "error": "错误信息",
  "errorStack": "错误堆栈"
}
```

## 控制台调试输出

除了localStorage存储，系统在浏览器控制台输出实时调试信息：

- `[PRODUCT_DETAIL_SUBMIT]`：商品详情提交开始
- `[PRODUCT_DETAIL_DATA]`：商品详情数据快照
- `[PRODUCT_VALIDATION]`：商品验证逻辑执行
- `[COMBO_VALIDATION]`：套餐验证详细过程
- `[ARRANGE_PARAMS_START]`：参数组装逻辑开始
- `[ORIGINAL_DATA]`：原始数据状态快照
- `[DATA_MERGE]`：数据合并处理过程
- `[FINAL_PARAMS]`：最终提交参数构造
- `[EXCHANGE_GOODS_START]`：兑换API调用开始
- `[EXCHANGE_RESPONSE]`：兑换API响应数据
- `[EXCHANGE_SUCCESS/FAILED]`：兑换结果状态

## 调试问题排查

### 常见调试问题

1. **日志查看器无数据**
   - 确认已完成完整的兑换流程
   - 检查localStorage是否被禁用
   - 验证调试代码是否正确执行

2. **比对工具JSON解析失败**
   - 验证抓包数据是否为有效JSON
   - 检查是否复制了完整的请求体
   - 确认JSON格式无语法错误

3. **调试数据过多影响性能**
   - 定期清空调试日志
   - 系统自动限制日志条数
   - 关注内存使用情况

### 调试技巧

1. **开发者工具**：实时查看控制台调试输出
2. **分步调试**：逐步执行流程，观察每个节点的数据变化
3. **时间戳对齐**：确保比对的是同一次操作的数据
4. **关键字段聚焦**：重点检查商品代码、数量、定制选项等核心字段

## 技术实现细节

### 调试代码注入位置

1. **HTTP拦截器**：`/utils/request.js` - logRequest函数拦截所有API
2. **商品验证逻辑**：`/pages/selectProduct/selectProductDetail.vue` - rightNowExec方法
3. **参数组装逻辑**：`/pages/pickingMeals/pickingMeals.vue` - arrangePrams方法
4. **兑换提交逻辑**：`/pages/pickingMeals/pickingMeals.vue` - exchangeGoods方法

### 存储实现

- 基于uni-app的localStorage API
- 自动限制调试日志数量（API日志100条，其他20-50条）
- 使用JSON.parse/stringify进行深拷贝
- 时间戳索引便于数据检索

### 比对算法实现

- 递归深度遍历JSON对象结构
- 支持数组、对象、原始类型的差异检测
- 生成详细的差异路径和类型报告
- 处理嵌套结构和循环引用

## 调试注意事项

1. **敏感数据**：调试日志可能包含用户数据，注意数据安全
2. **存储限制**：localStorage有容量限制，及时清理调试数据
3. **性能开销**：大量调试输出可能影响应用性能
4. **环境兼容**：确保目标环境支持localStorage和console API
5. **生产环境**：生产环境应移除或禁用调试代码

## 版本记录

- **v1.0.0**：基础调试日志记录和查看功能
- **v1.1.0**：数据差异比对工具
- **v1.2.0**：优化存储机制和自动清理

## 开发者备注

此调试系统仅用于开发和测试阶段的问题排查，不应在生产环境中保留。建议通过构建配置或环境变量控制调试代码的启用状态。