<template>
	<view class="">
		<t-index-address @select="select"></t-index-address>
	</view>
</template>

<script>
	export default {
		data() {
			return {
			
			}
		},
		methods: {
			select(data) {
				let latAndLng={
					latitude:data.lat,
					longitude:data.lng
				}
				this.$store.commit('updateParamsLatLon',latAndLng)
				console.log(latAndLng)
				uni.navigateBack({delta:1})
			}
		}
	}
</script>

<style>
</style>