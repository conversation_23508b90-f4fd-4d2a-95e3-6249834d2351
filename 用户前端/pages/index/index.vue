<template>
	<view class="container">
		<!-- 轮播图 -->
		<view class="swbox" ref="getswbox">
			<view class="title">
				美食兑换
			</view>
			<view class="swiper-box">
				<swiper autoplay="true" style="height:400rpx;" next-margin="100rpx">
					<swiper-item v-for="(item,index) in swiperList" :key="item.id">
						<image :src="item.img" alt="" />
					</swiper-item>
				</swiper>
			</view>
		</view>
		<!-- 五个卡片 -->
		<view class="card">
			<view class="card-item" v-for="(item,index) in cardList" :key="item.id">
				<view class="card-img">
					<image :src="item.img" alt="" />
				</view>
				<view class="card-title">
					{{item.name}}
				</view>
			</view>
		</view>
		<!-- 兑换卡片 -->
		<view class="exchange" style="background-image: url('../../static/images/indexImg/exchange-bg.png');">
			<view class="exchange-inp">
				<image src="../../static/images/indexImg/search.png" alt="" />
				<input type="text" placeholder="请输入兑换码" v-model="exchangeCode" />
			</view>
			<view class="exchange-btn" @click="choiseRestaurant">
				兑换/查询
			</view>
		</view>
		<!-- 取餐须知 -->
		<view class="takeorder" style="background-image: url('../../static/images/indexImg/takebg.png');">
			<view class="takeorder-title">
				取餐须知
			</view>
			<view class="takeorder-line">

			</view>
			<view class="takeorder-item" v-for="(item,index) in takeList" :key="item.id">
				<view class="takeorder-img">
					<image :src="item.img" alt="" mode="widthFix" />
				</view>
				<view class="takeorder-content">
					{{item.content}}
				</view>
			</view>
		</view>
		<!-- 取餐码获得方式 -->
		<view class="getway" style="background-image: url('../../static/images/indexImg/getway-bg.png');">
			<view class="getway-code">
				取餐码获得方式
			</view>
			<view class="getway-item" v-for="(item,index) in getwayList" :key="item.id">
				<view class="">
					<image :src="item.img" mode=""></image>
				</view>
				<view class="getway-content">
					{{item.content}}
				</view>
			</view>
			<view class="getway-bottom">
				<image src="../../static/images/indexImg/getway-bottom.png" mode=""></image>
			</view>
		</view>
	</view>
</template>

<script>
	import Alipay from 'alipayjs';
	import {
		checkCode
	} from '../../api/request'
	export default {
		data() {
			return {
				// 统一管理图片路径常量
			imagePaths: {
				card: ['card-one.png','card-two.png','card-three.png','card-four.png','card-five.png'],
				banner: 'banner-one.png',
				ake: ['take-one.png','take-two.png','take-three.png','take-four.png'],
				getway: ['getway-one.png','getway-two.png']
			},
			cardList: this.imagePaths.card.map((img, index) => ({
				id: index+1,
				img: `../../static/images/indexImg/${img}`,
				name: ['输兑换码','选择餐厅','选择餐品','等待取餐码','到店取餐'][index]
			})),
			swiperList: [1,2].map(id => ({
				id,
				img: `../../static/images/indexImg/${this.imagePaths.banner}`
			})),
			takeList: this.imagePaths.take.map((img, index) => ({
				id: index+1,
				img: `../../static/images/indexImg/${img}`,
				content: [
					'1.输入兑换码后点击立即兑换/查询，可以兑换指定的餐品，也可以查询已兑换的餐品的取餐码！',
					'2.进去兑换页面后，先选择您要提货的门店，选择您的餐品，兑换成功后即可凭取餐码前台取餐!',
					'3.出餐成功后，兑换码自动失效，出餐失败的兑换码可以重新兑换!',
					'4.兑换成功后，系统会在1-5分钟左右出餐，高峰期可能会出现延迟！'
					][index]
			})),
			getwayList: this.imagePaths.getway.map((img, index) => ({
				id: index+1,
				img: `../../static/images/indexImg/${img}`,
				content: [
					'在兑换页面再次输入兑换成功的兑换码，点击兑换/查询，可以查询订单状态和取餐码！',
					'出餐成功后，系统将为您发送取餐码短信！'
					][index]
			})),
				exchangeCode: '',
				alipay: null,
				kil:null
			}
		},
		mounted() {
		},
		methods: {
			async choiseRestaurant() {
				if (this.exchangeCode !== '') {
					let params = {
						code: this.code
					}
					let res = await checkCode(params)
					if (res.code === 200) {
						// 未使用状态
						uni.navigateTo({
							url: '../../cres?s=' + this.exchangeCode
						})
						this.$store.commit('updateCodeId', this.exchangeCode)
					} else if (res.code === 201) {
						//已使用订单未过期
						// let result=res.data.map(item=>{
						// 	return{
						// 		...item,orderActionList:{}
						// 	}
						// })
						// uni.navigateTo({
						// 	url:'/pages/pickingMeals/pickingMeals?list='+encodeURIComponent(JSON.stringify(result))
						// })
						uni.navigateTo({
							url: '/pages/pickingMeals/pickingMeals?s=' + this.exchangeCode
						})
					} else {
						uni.navigateTo({
							url: '../../cres?s=' + this.exchangeCode
						})
					}
				} else {
					uni.showToast({
						title: "请输入兑换码",
						icon: 'none',
						duration: 2000
					})
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.swbox {
			padding: 0 26rpx;

			.title {
				font-size: 36rpx;
				font-weight: 900;
				color: #000000;
				margin: 10rpx 0;
			}

			.swiper-box {
				.swiper {
					height: 400rpx;
				}

				.swiper-item {
					height: 400rpx;
				}

				image {
					margin: auto;
					height: 100%;
				}
			}
		}

		.card {
			display: flex;
			justify-content: space-around;
			margin-top: 49rpx;
			padding: 0 26rpx;

			.card-item {
				text-align: center;

				.card-img {
					width: 48rpx;
					height: 48rpx;
					margin-top: 49rpx;
					margin: auto;

					image {
						height: 100%;
						width: 100%;
					}
				}

				.card-title {
					margin-top: 16rpx;
				}
			}
		}

		.exchange {
			background-repeat: no-repeat;
			background-size: 100%;
			margin-top: 24rpx;
			padding: 152rpx 26rpx 0 26rpx;

			.exchange-inp {
				position: relative;

				image {
					width: 34rpx;
					height: 34rpx;
					position: absolute;
					left: 20rpx;
					top: 25rpx;
				}

				input {
					border: 1rpx solid #F8BA16;
					height: 80rpx;
					line-height: 80rpx;
					border-radius: 44rpx;
					box-shadow: 0rpx 4rpx 21rpx 0rpx rgba(211, 150, 2, 0.25);
					padding-left: 47rpx;
					font-size: 28rpx;
					padding-left: 57rpx;
				}
			}

			.exchange-btn {
				margin-top: 72rpx;
				background-color: #F8BA16;
				color: #ffffff;
				text-align: center;
				height: 88rpx;
				line-height: 88rpx;
				border-radius: 44rpx;
			}
		}

		.takeorder view:last-child {
			border: 0;
		}

		.takeorder {
			background-repeat: no-repeat;
			background-size: 100%;
			margin-top: 27rpx;
			padding: 0 26rpx;

			.takeorder-title {
				font-size: 500;
				font-size: 36rpx;
				color: #cb9847;
				text-align: center;
				padding-top: 51rpx;
			}

			.takeorder-line {
				background: #F8BA16;
				border-radius: 6rpx;
				border: 1rpx solid #CB9847;
				opacity: 0.16;
				width: 443rpx;
				height: 13rpx;
				background-color: #CB9847;
				margin: -5rpx auto;
			}

			.takeorder-item {
				padding-top: 26rpx;
				display: flex;
				border-bottom: 1rpx solid rgba(205, 207, 206, 0.23);
				padding: 52rpx 49rpx 52rpx 21rpx;
				font-family: SourceHanSansSC;

				.takeorder-img {
					margin-right: 10rpx;

					image {
						width: 62rpx;
						height: 74rpx;
					}
				}

				.takeorder-content {
					letter-spacing: 5rpx;
					line-height: 40rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #444745;
				}
			}
		}

		.getway view:nth-child(3) {
			border: 0;
		}

		.getway {
			padding: 0 26rpx;
			background-repeat: no-repeat;
			background-size: 100%;
			margin-top: 37rpx;

			.getway-code {
				text-align: center;
				font-family: Source Han Sans CN;
				font-weight: 500;
				font-size: 36rpx;
				color: #FFFFFF;
				line-height: 70rpx;
			}

			.getway-item {
				display: flex;
				padding: 30rpx;
				border-bottom: 1rpx solid rgba(205, 207, 206, 0.23);

				image {
					width: 33rpx;
					height: 33rpx;
					margin-right: 27rpx;
				}

				.getway-content {
					color: #444745;
					letter-spacing: 5rpx;
					line-height: 40rpx;
					font-size: 28rpx;
					font-weight: 400;
				}
			}

			.getway-bottom {
				image {
					height: 66rpx;
					width: 100%;
					left: 0;
					position: absolute;
					right: 0;
				}
			}
		}
	}
</style>