<template>
	<view class="log-compare">
		<view class="header">
			<text class="title">日志比对工具</text>
			<button @click="startCompare" class="compare-btn">开始比对</button>
		</view>
		
		<view class="input-section">
			<view class="input-group">
				<text class="label">抓包数据 (JSON格式):</text>
				<textarea 
					v-model="capturedData" 
					placeholder="请粘贴抓包获取的JSON数据"
					class="data-input"
				></textarea>
			</view>
			
			<view class="input-group">
				<text class="label">应用提交数据:</text>
				<picker @change="onLogSelect" :value="selectedLogIndex" :range="logOptions" range-key="label">
					<view class="picker">
						{{selectedLogIndex >= 0 ? logOptions[selectedLogIndex].label : '请选择日志记录'}}
					</view>
				</picker>
				<textarea 
					v-model="appData" 
					placeholder="选择日志记录后自动填充"
					class="data-input"
					readonly
				></textarea>
			</view>
		</view>
		
		<view v-if="compareResult" class="result-section">
			<view class="result-header">
				<text class="result-title">比对结果</text>
				<text :class="['status', compareResult.identical ? 'identical' : 'different']">
					{{compareResult.identical ? '数据一致' : '发现差异'}}
				</text>
			</view>
			
			<view v-if="!compareResult.identical" class="differences">
				<text class="diff-title">差异详情:</text>
				<view v-for="(diff, index) in compareResult.differences" :key="index" class="diff-item">
					<text class="diff-path">路径: {{diff.path}}</text>
					<view class="diff-values">
						<view class="value-item captured">
							<text class="value-label">抓包数据:</text>
							<text class="value-content">{{formatValue(diff.captured)}}</text>
						</view>
						<view class="value-item app">
							<text class="value-label">应用数据:</text>
							<text class="value-content">{{formatValue(diff.app)}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="actions">
				<button @click="exportCompareResult" class="export-btn">导出比对结果</button>
				<button @click="clearCompare" class="clear-btn">清空比对</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			capturedData: '',
			appData: '',
			selectedLogIndex: -1,
			logOptions: [],
			compareResult: null,
			allLogs: []
		}
	},
	onLoad() {
		this.loadLogs()
	},
	methods: {
		loadLogs() {
			try {
				// 加载所有相关日志
				const exchangeLogs = uni.getStorageSync('exchange_logs') || []
				const paramLogs = uni.getStorageSync('param_arrange_logs') || []
				
				this.allLogs = []
				this.logOptions = []
				
				// 添加兑换日志
				exchangeLogs.forEach((log, index) => {
					this.allLogs.push({
						type: 'exchange',
						data: log.requestParams,
						original: log
					})
					this.logOptions.push({
						label: `兑换请求 - ${this.formatTime(log.timestamp)}`,
						value: this.allLogs.length - 1
					})
				})
				
				// 添加参数组装日志
				paramLogs.forEach((log, index) => {
					this.allLogs.push({
						type: 'param',
						data: log.finalTrParams,
						original: log
					})
					this.logOptions.push({
						label: `参数组装 - ${this.formatTime(log.timestamp)}`,
						value: this.allLogs.length - 1
					})
				})
				
				// 按时间倒序排列
				this.logOptions.sort((a, b) => {
					const timeA = this.allLogs[a.value].original.timestamp
					const timeB = this.allLogs[b.value].original.timestamp
					return new Date(timeB) - new Date(timeA)
				})
				
			} catch (e) {
				console.error('加载日志失败:', e)
				uni.showToast({
					title: '加载日志失败',
					icon: 'none'
				})
			}
		},
		onLogSelect(e) {
			this.selectedLogIndex = e.detail.value
			if (this.selectedLogIndex >= 0) {
				const selectedLog = this.allLogs[this.logOptions[this.selectedLogIndex].value]
				this.appData = JSON.stringify(selectedLog.data, null, 2)
			}
		},
		startCompare() {
			if (!this.capturedData.trim()) {
				uni.showToast({
					title: '请输入抓包数据',
					icon: 'none'
				})
				return
			}
			
			if (!this.appData.trim()) {
				uni.showToast({
					title: '请选择应用数据',
					icon: 'none'
				})
				return
			}
			
			try {
				const captured = JSON.parse(this.capturedData)
				const app = JSON.parse(this.appData)
				
				this.compareResult = this.deepCompare(captured, app)
				
				console.log('比对结果:', this.compareResult)
				
			} catch (e) {
				console.error('比对失败:', e)
				uni.showToast({
					title: 'JSON格式错误',
					icon: 'none'
				})
			}
		},
		deepCompare(obj1, obj2, path = '') {
			const differences = []
			
			const compare = (a, b, currentPath) => {
				if (a === b) return
				
				if (typeof a !== typeof b) {
					differences.push({
						path: currentPath,
						captured: a,
						app: b,
						type: 'type_mismatch'
					})
					return
				}
				
				if (a === null || b === null) {
					if (a !== b) {
						differences.push({
							path: currentPath,
							captured: a,
							app: b,
							type: 'null_mismatch'
						})
					}
					return
				}
				
				if (Array.isArray(a) && Array.isArray(b)) {
					if (a.length !== b.length) {
						differences.push({
							path: currentPath + '.length',
							captured: a.length,
							app: b.length,
							type: 'array_length'
						})
					}
					
					const maxLength = Math.max(a.length, b.length)
					for (let i = 0; i < maxLength; i++) {
						compare(a[i], b[i], `${currentPath}[${i}]`)
					}
					return
				}
				
				if (typeof a === 'object' && typeof b === 'object') {
					const allKeys = new Set([...Object.keys(a), ...Object.keys(b)])
					
					allKeys.forEach(key => {
						const newPath = currentPath ? `${currentPath}.${key}` : key
						
						if (!(key in a)) {
							differences.push({
								path: newPath,
								captured: undefined,
								app: b[key],
								type: 'missing_in_captured'
							})
						} else if (!(key in b)) {
							differences.push({
								path: newPath,
								captured: a[key],
								app: undefined,
								type: 'missing_in_app'
							})
						} else {
							compare(a[key], b[key], newPath)
						}
					})
					return
				}
				
				// 基本类型不相等
				differences.push({
					path: currentPath,
					captured: a,
					app: b,
					type: 'value_mismatch'
				})
			}
			
			compare(obj1, obj2, '')
			
			return {
				identical: differences.length === 0,
				differences: differences,
				timestamp: new Date().toISOString()
			}
		},
		formatValue(value) {
			if (value === undefined) return 'undefined'
			if (value === null) return 'null'
			if (typeof value === 'string') return `"${value}"`
			if (typeof value === 'object') return JSON.stringify(value)
			return String(value)
		},
		formatTime(timestamp) {
			const date = new Date(timestamp)
			return date.toLocaleString('zh-CN')
		},
		exportCompareResult() {
			if (!this.compareResult) {
				uni.showToast({
					title: '请先进行比对',
					icon: 'none'
				})
				return
			}
			
			const exportData = {
				compareResult: this.compareResult,
				capturedData: this.capturedData,
				appData: this.appData,
				exportTime: new Date().toISOString()
			}
			
			console.log('=== 比对结果导出 ===', JSON.stringify(exportData, null, 2))
			
			uni.showModal({
				title: '比对结果已导出',
				content: '比对结果已输出到控制台，请查看开发者工具',
				showCancel: false
			})
		},
		clearCompare() {
			this.capturedData = ''
			this.appData = ''
			this.selectedLogIndex = -1
			this.compareResult = null
		}
	}
}
</script>

<style lang="scss" scoped>
.log-compare {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	margin-bottom: 30rpx;
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.compare-btn {
		padding: 15rpx 30rpx;
		background-color: #007aff;
		color: white;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
	}
}

.input-section {
	margin-bottom: 40rpx;
	
	.input-group {
		margin-bottom: 30rpx;
		
		.label {
			display: block;
			font-size: 28rpx;
			color: #333;
			margin-bottom: 15rpx;
			font-weight: bold;
		}
		
		.picker {
			padding: 20rpx;
			background-color: #fff;
			border: 1rpx solid #ddd;
			border-radius: 8rpx;
			margin-bottom: 15rpx;
			font-size: 28rpx;
			color: #333;
		}
		
		.data-input {
			width: 100%;
			height: 300rpx;
			padding: 20rpx;
			background-color: #fff;
			border: 1rpx solid #ddd;
			border-radius: 8rpx;
			font-size: 24rpx;
			line-height: 1.4;
			box-sizing: border-box;
		}
	}
}

.result-section {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	
	.result-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #e5e5e5;
		
		.result-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
		
		.status {
			padding: 8rpx 16rpx;
			border-radius: 6rpx;
			font-size: 24rpx;
			color: white;
			
			&.identical {
				background-color: #34c759;
			}
			
			&.different {
				background-color: #ff3b30;
			}
		}
	}
	
	.differences {
		margin-bottom: 30rpx;
		
		.diff-title {
			font-size: 28rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
			display: block;
		}
		
		.diff-item {
			margin-bottom: 25rpx;
			padding: 20rpx;
			background-color: #f8f8f8;
			border-radius: 8rpx;
			border-left: 4rpx solid #ff3b30;
			
			.diff-path {
				font-size: 24rpx;
				color: #666;
				margin-bottom: 15rpx;
				display: block;
				font-weight: bold;
			}
			
			.diff-values {
				.value-item {
					margin-bottom: 10rpx;
					
					.value-label {
						font-size: 22rpx;
						color: #999;
						margin-right: 10rpx;
					}
					
					.value-content {
						font-size: 24rpx;
						word-break: break-all;
					}
					
					&.captured .value-content {
						color: #ff6b6b;
					}
					
					&.app .value-content {
						color: #4ecdc4;
					}
				}
			}
		}
	}
	
	.actions {
		display: flex;
		gap: 20rpx;
		
		button {
			flex: 1;
			padding: 20rpx;
			border: none;
			border-radius: 8rpx;
			font-size: 28rpx;
		}
		
		.export-btn {
			background-color: #007aff;
			color: white;
		}
		
		.clear-btn {
			background-color: #8e8e93;
			color: white;
		}
	}
}
</style>