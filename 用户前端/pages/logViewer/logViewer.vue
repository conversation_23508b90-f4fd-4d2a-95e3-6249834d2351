<template>
	<view class="log-viewer">
		<view class="header">
			<text class="title">日志查看器</text>
			<view class="actions">
				<button @click="exportLogs" class="export-btn">导出日志</button>
				<button @click="clearLogs" class="clear-btn">清空日志</button>
			</view>
		</view>
		
		<view class="tabs">
			<view 
				v-for="(tab, index) in tabs" 
				:key="index"
				:class="['tab', {active: activeTab === index}]"
				@click="switchTab(index)"
			>
				{{tab.name}} ({{tab.count}})
			</view>
		</view>
		
		<scroll-view scroll-y class="log-content">
			<view v-if="currentLogs.length === 0" class="no-logs">
				暂无日志数据
			</view>
			<view v-else>
				<view 
					v-for="(log, index) in currentLogs" 
					:key="index"
					class="log-item"
					@click="toggleLogDetail(index)"
				>
					<view class="log-header">
						<text class="timestamp">{{formatTime(log.timestamp)}}</text>
						<text :class="['type', log.type.toLowerCase()]">{{log.type}}</text>
					</view>
					<view class="log-summary">
						<text v-if="log.url">{{log.method}} {{log.url}}</text>
						<text v-else-if="log.type === 'PARAM_ARRANGEMENT'">参数组装</text>
						<text v-else-if="log.type === 'EXCHANGE_RESULT'">兑换结果 - {{log.resultCode}}</text>
						<text v-else>{{log.type}}</text>
					</view>
					<view v-if="expandedLogs[index]" class="log-detail">
						<pre>{{JSON.stringify(log, null, 2)}}</pre>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			activeTab: 0,
			expandedLogs: {},
			apiLogs: [],
			productDetailLogs: [],
			paramArrangeLogs: [],
			exchangeLogs: [],
			exchangeErrorLogs: []
		}
	},
	computed: {
		tabs() {
			return [
				{ name: 'API请求', count: this.apiLogs.length },
				{ name: '商品详情', count: this.productDetailLogs.length },
				{ name: '参数组装', count: this.paramArrangeLogs.length },
				{ name: '兑换结果', count: this.exchangeLogs.length },
				{ name: '错误日志', count: this.exchangeErrorLogs.length }
			]
		},
		currentLogs() {
			switch(this.activeTab) {
				case 0: return this.apiLogs.slice().reverse()
				case 1: return this.productDetailLogs.slice().reverse()
				case 2: return this.paramArrangeLogs.slice().reverse()
				case 3: return this.exchangeLogs.slice().reverse()
				case 4: return this.exchangeErrorLogs.slice().reverse()
				default: return []
			}
		}
	},
	onLoad() {
		this.loadLogs()
	},
	onShow() {
		this.loadLogs()
	},
	methods: {
		loadLogs() {
			try {
				this.apiLogs = uni.getStorageSync('api_logs') || []
				this.productDetailLogs = uni.getStorageSync('product_detail_logs') || []
				this.paramArrangeLogs = uni.getStorageSync('param_arrange_logs') || []
				this.exchangeLogs = uni.getStorageSync('exchange_logs') || []
				this.exchangeErrorLogs = uni.getStorageSync('exchange_error_logs') || []
			} catch (e) {
				console.error('加载日志失败:', e)
				uni.showToast({
					title: '加载日志失败',
					icon: 'none'
				})
			}
		},
		switchTab(index) {
			this.activeTab = index
			this.expandedLogs = {}
		},
		toggleLogDetail(index) {
			this.$set(this.expandedLogs, index, !this.expandedLogs[index])
		},
		formatTime(timestamp) {
			const date = new Date(timestamp)
			return date.toLocaleString('zh-CN')
		},
		exportLogs() {
			const allLogs = {
				apiLogs: this.apiLogs,
				productDetailLogs: this.productDetailLogs,
				paramArrangeLogs: this.paramArrangeLogs,
				exchangeLogs: this.exchangeLogs,
				exchangeErrorLogs: this.exchangeErrorLogs,
				exportTime: new Date().toISOString()
			}
			
			console.log('=== 导出的完整日志数据 ===', JSON.stringify(allLogs, null, 2))
			
			uni.showModal({
				title: '日志已导出',
				content: '日志数据已输出到控制台，请查看开发者工具',
				showCancel: false
			})
		},
		clearLogs() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空所有日志吗？此操作不可恢复',
				success: (res) => {
					if (res.confirm) {
						try {
							uni.removeStorageSync('api_logs')
							uni.removeStorageSync('product_detail_logs')
							uni.removeStorageSync('param_arrange_logs')
							uni.removeStorageSync('exchange_logs')
							uni.removeStorageSync('exchange_error_logs')
							this.loadLogs()
							uni.showToast({
								title: '日志已清空',
								icon: 'success'
							})
						} catch (e) {
							console.error('清空日志失败:', e)
							uni.showToast({
								title: '清空失败',
								icon: 'none'
							})
						}
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.log-viewer {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: #fff;
	border-bottom: 1rpx solid #e5e5e5;
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.actions {
		display: flex;
		gap: 20rpx;
		
		button {
			padding: 10rpx 20rpx;
			font-size: 24rpx;
			border-radius: 8rpx;
			border: none;
		}
		
		.export-btn {
			background-color: #007aff;
			color: white;
		}
		
		.clear-btn {
			background-color: #ff3b30;
			color: white;
		}
	}
}

.tabs {
	display: flex;
	background-color: #fff;
	border-bottom: 1rpx solid #e5e5e5;
	
	.tab {
		flex: 1;
		padding: 20rpx;
		text-align: center;
		font-size: 28rpx;
		color: #666;
		border-bottom: 4rpx solid transparent;
		
		&.active {
			color: #007aff;
			border-bottom-color: #007aff;
			font-weight: bold;
		}
	}
}

.log-content {
	flex: 1;
	padding: 20rpx;
}

.no-logs {
	text-align: center;
	padding: 100rpx;
	color: #999;
	font-size: 28rpx;
}

.log-item {
	background-color: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 24rpx;
	border: 1rpx solid #e5e5e5;
	
	.log-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12rpx;
		
		.timestamp {
			font-size: 24rpx;
			color: #999;
		}
		
		.type {
			padding: 4rpx 12rpx;
			border-radius: 6rpx;
			font-size: 20rpx;
			color: white;
			
			&.request {
				background-color: #007aff;
			}
			
			&.response {
				background-color: #34c759;
			}
			
			&.error {
				background-color: #ff3b30;
			}
			
			&.product_submit_validation {
				background-color: #ff9500;
			}
			
			&.param_arrangement {
				background-color: #5856d6;
			}
			
			&.exchange_result {
				background-color: #30d158;
			}
			
			&.exchange_error {
				background-color: #ff453a;
			}
		}
	}
	
	.log-summary {
		font-size: 28rpx;
		color: #333;
		line-height: 1.4;
	}
	
	.log-detail {
		margin-top: 20rpx;
		padding: 20rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		
		pre {
			font-size: 22rpx;
			color: #666;
			white-space: pre-wrap;
			word-break: break-all;
			line-height: 1.4;
		}
	}
}
</style>