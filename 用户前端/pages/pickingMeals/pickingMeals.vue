<template>
	<view class="container" v-if="wayShow==true">
		<!-- 门店信息  style="background-image: url('../../static/images/pickingMeals/background.png');" :style="{backgroundImage:url('url')}"-->
		<view class="storeinfo" :style="{backgroundImage:`url(${bgimage})`}">
			
			<view class="takeorder">
				<!-- <view class="takeorder-top">
					<view class="back" @click="goBack">
						<image src="../../static/arrow.png" mode=""></image>
					</view>
					<view class="title">
						柜台取餐
					</view>
					<view class="">

					</view>
				</view> -->
				 
			</view>
			
		</view>
	<view class="storeinfo-details">
	  <view class="store-name">{{ storeName }}</view>
	  <view class="store-address">{{ storeAddress }}</view>
	</view>
		<view class="takeorder-bottom" style="background-color: #ffffff;" v-if="fiveZeroOne">
			<block v-if="mockList.length>0">
				<view class="detail">
					<view class="detail-left">
						<view class="detail-left-top">
							<img src="../../static/images/pickingMeals/store-icon.png" alt="" class="res-icon" />
							<view class="restaurant">
								{{mockList[0].storeName}}
							</view>
						</view>
						<view class="detail-left-bottom">
							<image src="../../static/images/pickingMeals/address-icon.png" mode=""></image>
							<view class="address">
								{{mockList[0].storeAddress}}
							</view>
						</view>
					</view>
				</view>
			</block>
			<block v-else>
				<view class="detail">
					<view class="detail-left">
						<view class="detail-left-top">
							<img src="../../static/images/pickingMeals/store-icon.png" alt="" class="res-icon" />
							<view class="restaurant">
								{{storeCode.name}}
							</view>
						</view>
						<view class="detail-left-bottom">
							<image src="../../static/images/pickingMeals/address-icon.png" mode=""></image>
							<view class="address">
								{{storeCode.address}}
							</view>
						</view>
					</view>
					<!-- 					<view class="detail-right" style="background-image: url('../../static/images/pickingMeals/distance.png');"
						v-if="storeCode.distanceText">
						<view class="distance">
							距您{{storeCode.distanceText}}
						</view>
					</view> -->
				</view>
			</block>
		</view>
		<!-- 就餐方式 -->
		<view class="repast" v-if="orderShow">
			<view class="repast-title">
				<image src="../../static/images/pickingMeals/dining.png" mode=""></image>
				<view class="repast-title-way">
					就餐方式
				</view>
			</view>
			<view class="repast-way">
				<block v-for="(item,index) in takeFoodWay" :key="index">
					<view :class="item.checked?'repast-way-active':'repast-way-item'" style="width: calc(90% / 3);"
						@click="choiceWay(index)">
						<view>{{item.text}}</view>
					</view>
				</block>
			</view>
			<view class="repast-contact">
				<view class="repast-contact-title">
					<image src="../../static/images/pickingMeals/contact.png" mode=""></image>
					<view class="contact-info">
						联系方式
					</view>
				</view>
				<view class="repast-contact-input">
					<input type="number" name="" id="" placeholder="请输入手机号(选填)" v-model="userPhoneNum">
				</view>
			</view>
		</view>
		<view class="pickup" v-else>
			<view class="requirebox" v-if="!allGoodShow">
				<van-loading color="#F8BA16" size="50px" />
				<view class="require">正在查询取餐码...</view>
			</view>
			<view class="ordercode" v-else>
				<block v-for="(moItem,moIndex) in mockList" :key="moIndex">
					<block v-if="moItem.code==501">
						<view class="ordercode-title">
							取餐码
						</view>
						<view class="shop-info">
							<view class="eating-name" style="text-align: center;">{{moItem.message}}</view>
						</view>
					</block>
					<block v-else>
						<view class="ordercode-title">
							取餐码
						</view>
						<view class="shop-info">
							<block v-for="(item,index) in moItem.orderProductList" :key="index">
								<block v-if="item.productType==1 || item.productType==3">
									<view class="shop-item">
										<view class="eating">
											<!-- <image :src="item.productImage" mode=""></image> -->
											<view class="eating-name">{{item.productName}}</view>
										</view>
										<view class="orderNum">
											×{{item.quantity}}
										</view>
									</view>
								</block>
								<block v-for="(items,indexs) in item.comboItemList" :key="indexs" v-else>
									<view class="shop-item">
										<view class="eating">
											<view class="eating-name">{{items.name}}</view>
										</view>
										<view class="orderNum">
											×{{item.quantity}}
										</view>
									</view>
								</block>
							</block>
							<view class="opera">
								<text class="orderBox">{{moItem.pickupCode ? '取餐码'+ moItem.pickupCode :"出餐中,请等待..."}}</text>
								<text class="lookShot" @click="goMealCode(moItem)">查看截图</text>
							</view>
						</view>
					</block>
				</block>
			</view>
		</view>
		<!-- 餐品详情 v-if="fiveZeroOne"-->
			<view class="mealsbox mealsbox-bg" v-if="mockList.length>0">
				<view class="mealsbox-title">
					餐品详情
				</view>
				<block v-for="(moItem,moIndex) in mockList" :key="moIndex">
					<block v-for="(item,index) in moItem.orderProductList" :key="index">
						<block>
							<view class="mealsbox-detail">
								<view class="mealsbox-detail-left">
									<image :src="item.productImage" mode=""></image>
								</view>
								<view class="mealsbox-detail-center">
									<div class="bigTitle">{{item.productName}}</div>
									<view class="packageSub" v-for="(items,indexs) in item.comboItemList" :key="indexs">
										<view style="display: flex;">
											<view class="packageName">
												{{items.name.split(' ')[0]}}
											</view>
											<view class="packageNum">
												×{{item.quantity}}
											</view>
										</view>
										<view>{{items.name.split(' ')[1]}}</view>
									</view>
								</view>
							</view>
						</block>
					</block>
				</block>
			</view>
			<view class="mealsbox" style="background: radial-gradient(circle, #5396FB,#fff);"
				v-else>
				<view class="mealsbox-title">
					餐品详情
				</view>
				<block v-for="(prolist,allindex) in packageAllList" :key="allindex">
					<div class="orderTitle">{{prolist.productName}}</div>
					<block v-if="prolist.type===1 || prolist.type===3">
						<view class="mealsbox-detail">
							<view class="mealsbox-detail-left">
								<image :src="prolist.image" mode=""></image>
							</view>
							<view class="mealsbox-detail-center">
								<view class="bigTitle">{{prolist.name}}</view>
								<view class="packageSub">
									<view class="" style="display: flex;">
										<view class="packageName" style="">
											{{prolist.name}}
										</view>
										<view class="packageNum">
											×{{prolist.quantity || 1}}
										</view>
									</view>
									<view>
										<block v-if="prolist.customization && prolist.customization.options.length>0">
											<block v-for="(optItem,opIndex) in prolist.customization.options" :key="opIndex">
												<block v-if="optItem.checked==0">
													(不要{{optItem.name}})
												</block>
											</block>
										</block>
									</view>
								</view>
							</view>
						</view>
					</block>
					<block v-else>
						<view class="mealsbox-detail">
							<view class="mealsbox-detail-left">
								<image :src="prolist.image" mode=""></image>
							</view>
							<view class="">
								<view class="mealsbox-detail-center">
									<view class="bigTitle">{{prolist.name}}</view>
									<block v-for="(culist,cuindex) in prolist.comboItems" :key="cuindex">
										<view class="packageSub" v-for="(comlist,comindex) in culist.comboProducts" :key="comindex">
											<block v-if="comlist.isDefault==1">
												<view style="margin-bottom: 22rpx;">
													<view style="display: flex;">
														<view class="packageName">
															{{comlist.name}}
														</view>
														<view class="packageNum">
															×{{comlist.quantity?comlist.quantity:1}}
														</view>
													</view>
													<view style="display: flex;">
														<block v-if="comlist.customization && comlist.customization.items.length>0">
															<block v-for="(custItem,custIndex) in comlist.customization.items" :key="custIndex">
																<block v-for="(vaItem,vaIndex) in custItem.values" :key="vaIndex">
																	<block v-if="vaItem.checked==1" class="useSeled">
																		({{vaItem.name}})
																	</block>
																</block>
															</block>
														</block>
														<block v-if="comlist.customization && comlist.customization.options.length>0">
															<block v-for="(custItem,custIndex) in comlist.customization.options" :key="custIndex">
																<block v-if="custItem.checked==0" class="useSeled">
																	(不要{{custItem.name}})
																</block>
															</block>
														</block>
													</view>
												</view>
											</block>
										</view>
									</block>
								</view>
							</view>
						</view>
					</block>
				</block>
				<view class="amount">
					<view class="amount-num">
						共计 {{totalNum ? totalNum :1}}件，合计
					</view>
					<view class="amount-mark">
						￥
					</view>
					<view class="money">
						0
					</view>
				</view>
			</view>
		<view class="btn" @click="redeemNow" v-if="orderShow">
			立即兑换
		</view>
		<view class="btn" v-else>
			<view class="handlestart" @click="handleOnemore">点击刷新</view>
		</view>
	</view>
	<!-- 	<view class="" v-else
		style="background-image: url('../../static/images/product/background.png');background-repeat: no-repeat;background-size: 100%;height: 229rpx;display: flex;align-items: center;color: #ffffff;padding-left: 30px;margin-top: 150rpx;">
		暂无数据
	</view> -->
</template>

<script>
	import {
		exchangeGoods,
		getOrderWay,
		checkCode
	} from '@/api/request.js'
	import {
		startDemo
	} from '@/utils/dealDate.js'
	import checkCodeStatus from '../../mixin/checkCodeStatus'
	export default {
		// mixins:[checkCodeStatus],
		data() {
			return {
				trParams: {},
				userPhoneNum: '',
				takeFoodWay: [],
				orderShow: true,
				allGoodShow: false,
				newList: [],
				mockList: [],
				code: '',
				wayShow: "",
				fiveZeroOne: false,
				localStoreInfo: {
				        topBgImg: ''
			  },
			}
		},
		methods: {
			handleOnemore() {
				// this.choiseRestaurant()
				location.reload();
			},
			async choiseRestaurant() {
				this.getOrderWay()
				if (this.code !== '') {
					let params = {
						code: this.code
					}
					let res = await checkCode(params)
					if (res.code == 201) {
						this.mockList = res.data
						this.wayShow = true
						// if(res.data.every(item=>item.orderStatusCode=='1')){
						//  this.orderShow = false
						// }
						if (res.code == 201 && res.data == '') {
							uni.showModal({
								content: '当前兑换码不能使用!',
								showCancel: false,
								success: (res) => {
									uni.redirectTo({
										url: '/pages/index/index'
									})
								}
							})
						}
						return true
						//去查询
					} else if (res.code !== 200) {
						uni.showLoading({
							title: "当前兑换码不能使用!",
							mask: true
						})
					}
					return true
				} else {
					uni.showLoading({
						title: "请输入兑换码",
						mask: true
					})
					return false
				}
				return true
			},
			goBack() {
				uni.navigateBack({
					delta: 1
				})
			},
			arrangePrams() {
				// 提交订单参数 - 详细日志记录
				const timestamp = new Date().toISOString()
				console.log(`[ARRANGE_PARAMS_START] ${timestamp} 开始组装提交参数`)
				
				// 记录原始数据状态
				console.log(`[ORIGINAL_DATA] ${timestamp} 原始store数据:`, {
					packageParams: JSON.parse(JSON.stringify(this.$store.state.packageParams)),
					packageAllList: JSON.parse(JSON.stringify(this.packageAllList)),
					storeCode: this.$store.state.storeCode,
					codeId: this.$store.state.codeId,
					userPhoneNum: this.userPhoneNum,
					takeFoodWay: this.takeFoodWay
				})

				this.$store.state.packageParams.forEach((item, itemIndex) => {
					console.log(`[PACKAGE_PARAM_PROCESS] ${timestamp} 处理packageParams[${itemIndex}]:`, item)
					
					this.packageAllList.forEach((allItem, allIndex) => {
						item.POSITIVE.forEach((poImte, posIndex) => {
							if (poImte.code == allItem.code) {
								console.log(`[DATA_MERGE] ${timestamp} 合并数据 packageParams[${itemIndex}].POSITIVE[${posIndex}] <- packageAllList[${allIndex}]:`, {
									original: JSON.parse(JSON.stringify(poImte)),
									new: JSON.parse(JSON.stringify(allItem))
								})
								poImte = allItem
							}
						})
					})
				})
				
				let code = this.takeFoodWay.filter(item => item.checked == true).map(item => item.code).join('')
				console.log(`[TAKE_FOOD_WAY] ${timestamp} 选择的取餐方式:`, {
					selected: this.takeFoodWay.filter(item => item.checked),
					code: code
				})
				
				this.trParams = {
					foodData: this.$store.state.packageParams,
					storeCode: this.$store.state.storeCode.code,
					code: this.$store.state.codeId,
					iphone: this.userPhoneNum,
					TakeAMeal: code,
					storeName: this.$store.state.storeCode.name
				}
				
				console.log(`[FINAL_PARAMS] ${timestamp} 最终提交参数:`, JSON.parse(JSON.stringify(this.trParams)))
				
				// 保存详细的参数组装日志
				try {
					const paramLogs = uni.getStorageSync('param_arrange_logs') || []
					paramLogs.push({
						timestamp,
						type: 'PARAM_ARRANGEMENT',
						originalPackageParams: JSON.parse(JSON.stringify(this.$store.state.packageParams)),
						originalPackageAllList: JSON.parse(JSON.stringify(this.packageAllList)),
						finalTrParams: JSON.parse(JSON.stringify(this.trParams)),
						storeInfo: this.$store.state.storeCode,
						codeId: this.$store.state.codeId,
						userPhoneNum: this.userPhoneNum,
						takeFoodWayCode: code
					})
					if (paramLogs.length > 30) {
						paramLogs.splice(0, paramLogs.length - 30)
					}
					uni.setStorageSync('param_arrange_logs', paramLogs)
				} catch (e) {
					console.error('保存参数组装日志失败:', e)
				}
				// this.trParams.forEach((trItem, trIndex) => {
				// 			trItem.storeCode = this.$store.state.storeCode.code,
				// 			trItem.code = this.code,
				// 			trItem.iphone = this.userPhoneNum,
				// 			trItem.smId = trItem.smId,
				// 			trItem.TakeAMeal = code
				// })
			},
			// 立即兑换提交
			async exchangeGoods() {
				const timestamp = new Date().toISOString()
				console.log(`[EXCHANGE_GOODS_START] ${timestamp} 开始提交兑换请求`)
				console.log(`[EXCHANGE_PARAMS] ${timestamp} 提交参数:`, JSON.parse(JSON.stringify(this.trParams)))
				
				this.orderShow = false
				
				try {
					let res = await exchangeGoods(this.trParams)
					
					console.log(`[EXCHANGE_RESPONSE] ${timestamp} 兑换响应:`, JSON.parse(JSON.stringify(res)))
					
					this.mockList = res.data
					console.log(`[MOCK_LIST_UPDATE] ${timestamp} 更新mockList:`, this.mockList)
					
					// 保存兑换结果日志
					try {
						const exchangeLogs = uni.getStorageSync('exchange_logs') || []
						exchangeLogs.push({
							timestamp,
							type: 'EXCHANGE_RESULT',
							requestParams: JSON.parse(JSON.stringify(this.trParams)),
							response: JSON.parse(JSON.stringify(res)),
							mockList: this.mockList,
							resultCode: res.code
						})
						if (exchangeLogs.length > 20) {
							exchangeLogs.splice(0, exchangeLogs.length - 20)
						}
						uni.setStorageSync('exchange_logs', exchangeLogs)
					} catch (e) {
						console.error('保存兑换日志失败:', e)
					}
					
					if (res.code == 200) {
						console.log(`[EXCHANGE_SUCCESS] ${timestamp} 兑换成功`)
						if (res.data !== '') {
							this.allGoodShow = true
						} else {
							this.allGoodShow = false
						}
					} else {
						console.log(`[EXCHANGE_FAILED] ${timestamp} 兑换失败，错误码: ${res.code}`)
						this.orderShow = !this.orderShow
						this.allGoodShow = true
						this.fiveZeroOne = true
					}
				} catch (error) {
					console.error(`[EXCHANGE_ERROR] ${timestamp} 兑换请求异常:`, error)
					
					// 保存错误日志
					try {
						const errorLogs = uni.getStorageSync('exchange_error_logs') || []
						errorLogs.push({
							timestamp,
							type: 'EXCHANGE_ERROR',
							requestParams: JSON.parse(JSON.stringify(this.trParams)),
							error: error.toString(),
							errorStack: error.stack
						})
						if (errorLogs.length > 10) {
							errorLogs.splice(0, errorLogs.length - 10)
						}
						uni.setStorageSync('exchange_error_logs', errorLogs)
					} catch (e) {
						console.error('保存错误日志失败:', e)
					}
					
					this.orderShow = !this.orderShow
					this.allGoodShow = true
					this.fiveZeroOne = true
				}
				// this.newList = this.packageAllList
				// res.data.forEach((item, index) => {
				// 	this.newList.forEach((neItem, neIndex) => {
				// 		if (index === neIndex) {
				// 			neItem.orderCode = item
				// 		}
				// 	})
				// })
			},
			redeemNow() {
				if(!this.userPhoneNum){
					this.userPhoneNum = '18888888888'
				}
				this.arrangePrams()
				this.exchangeGoods()
			},
			isValidPhoneNumber(phoneNumber) {
				const regex = /^1[3-9]\d{9}$/;
				return regex.test(phoneNumber);
			},
			// 获取就餐方式
			async getOrderWay() {
				let params = this.$store.state.storeCode.code
				let res = await getOrderWay({
					storeCode: params
				})
				if (res.data && res.data.length > 0) {
					this.wayShow = true
					this.takeFoodWay = res.data.map(item => {
						return {
							...item,
							checked: false
						}
					})
					this.takeFoodWay[0].checked = true
					console.log(this.takeFoodWay, 'this.takeFoodWay');
					// if (this.takeFoodWay.length == 0) {
					// 	let res = await getOrderWay(params)
					// }
				}
				// else {
				// 	uni.showModal({
				// 		content: '暂不支持取餐，请换家门店试试试~',
				// 		showCancel: false,
				// 		success: (res) => {
				// 			uni.redirectTo({
				// 				url: '/pages/index/index'
				// 			})
				// 		}
				// 	})
				// }
			},
			// 选择就餐方式
			choiceWay(index) {
				this.takeFoodWay.forEach(item => {
					item.checked = false
				})
				this.takeFoodWay[index].checked = !this.takeFoodWay[index].checked
			},
			goMealCode(item) {
				item.orderActionList = ''
				try {
					uni.setStorageSync('mydata', JSON.stringify(item));
				} catch (e) {
					// error
				}
				uni.navigateTo({
					url: '/pages/mealCode/mealCode?list=' + encodeURIComponent(JSON.stringify(item))
				})
			},
		},
		

		async onLoad(e) {
		

			this.code = e.s
			
			let flag = await this.choiseRestaurant()
			if (!flag) {
				return
			}
			if (this.mockList.length > 0) {
				this.orderShow = false
				this.allGoodShow = true
			}
		},
		mounted() {},
		computed: {
		 storeName() {
		    if (this.mockList.length > 0 && this.mockList[0].storeName) {
		      return this.mockList[0].storeName;
		    }
		    return this.$store.state.storeCode.name || '默认门店名称';
		  },
		  storeAddress() {
		    if (this.mockList.length > 0 && this.mockList[0].storeAddress) {
		      return this.mockList[0].storeAddress;
		    }
		    return this.$store.state.storeCode.address || '默认门店地址';
		  },
			proLists() {
				return this.$store.state.proLists
			},
			packageAllList() {
				return this.$store.state.packageAllList
			},
			// mockList(){
			// 	return this.$store.state.mockList
			// },
			totalNum() {
		return this.packageAllList.reduce((total, packItem) => {
			if ((packItem.type === 1 || packItem.type === 3) && !packItem.comboItems) {
				return total + 1;
			}
			if (packItem.comboItems) {
				return total + packItem.comboItems.reduce((sum, combItem) => {
					return sum + combItem.comboProducts.filter(item => item.isDefault).reduce((s, i) => s + i.quantity, 0);
				}, 0);
			}
			return total;
		}, 0);
		},
			storeCode() {
				return this.$store.state.storeCode
			},
			bgimage() {
				if (this.$store.state.storeCode.mainPic) {
					
					return this.$store.state.storeCode.mainPic
				} else {
					return 'https://img.mcd.cn/gallery/30362aa4e4d9eb6e.png'
				}
			}
		}
	}
</script>

<style>
	pages {
		background: #F8F7F8;
	}
</style>
<style lang="scss" scoped>
	.container {
		background-color: #F8F7F8;
		position: relative;
		background: #F8F7F8;
		min-height: 100vh;

		.storeinfo {
			height: 420rpx;
			background-repeat: no-repeat;
			background-size: 100%;
			// position: relative;
			.takeorder {
				// position: absolute;
				// top: 100rpx;
				// left: 0;
				// right: 0;

				.takeorder-top {
					display: flex;
					justify-content: space-between;

					.back {
						image {
							width: 45rpx;
							height: 45rpx;
							transform: rotate(180deg);
						}
					}

					.title {
						font-family: Source Han Sans SC;
						font-weight: bold;
						font-size: 36rpx;
						color: #FFFFFF;
						text-align: center;
					}
				}

				.takeorder-top view {
					flex: 1;
				}
			}
		}
		
		.storeinfo-details {
		  margin-top: 20rpx;
		  margin-left: 30rpx;
		  margin-right: 30rpx;
		  padding: 30rpx 40rpx;
		  background-color: #fff; /* 白色卡片背景 */
		  border-radius: 20rpx;
		  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
		  color: #333333;
		  text-align: center;
		}
		
		.store-name {
		  font-size: 36rpx;
		  font-weight: 700;
		  line-height: 44rpx;
		  margin-bottom: 10rpx;
		}
		
		.store-address {
		  font-size: 28rpx;
		  font-weight: 400;
		  line-height: 36rpx;
		  color: #666666;
		  margin-bottom: 20rpx;
		}
		
	
		.takeorder-bottom {
			margin: 32rpx;
			background-repeat: no-repeat;
			background-size: 100%;

			.shop {
				display: inline-block;
				font-family: Source Han Sans SC;
				font-weight: 400;
				font-size: 28rpx;
				color: #FFBC0D;
				line-height: 36rpx;
				padding: 33rpx;
			}

			.detail {
				display: flex;
				justify-content: space-between;
				padding: 15rpx;
				position: relative;

				.detail-left {

					.detail-left-top {
						display: flex;
						align-items: center;

						.res-icon {
							width: 40rpx;
							height: 36rpx;
						}

						.restaurant {
							margin-left: 12rpx;
							font-family: Source Han Sans SC;
							font-weight: 500;
							font-size: 36rpx;
							color: #333333;
						}
					}

					.detail-left-bottom {
						display: flex;
						align-items: center;
						margin-top: 15rpx;

						image {
							width: 36rpx;
							height: 40rpx;
						}

						.address {
							font-family: Source Han Sans SC;
							font-weight: 400;
							font-size: 26rpx;
							color: #666666;
							line-height: 36rpx;
							margin-left: 12rpx;
						}
					}
				}

				.detail-right {
					background-repeat: no-repeat;
					background-size: 100%;
					width: 150rpx;
					height: 61rpx;
					position: absolute;
					right: 29rpx;
					top: -10rpx;
					text-align: center;
					margin-bottom: 10rpx;

					.distance {
						font-family: Source Han Sans SC;
						font-weight: 500;
						font-size: 24rpx;
						color: #333333;
						padding: 13rpx;
					}
				}
			}
		}

		.repast {
			margin: 32rpx;
			background-color: #FFFFFF;
			border-radius: 20rpx;
			padding: 38rpx;

			.repast-title {
				display: flex;

				image {
					width: 30rpx;
					height: 30rpx;
				}

				.repast-title-way {
					font-family: Source Han Sans SC;
					font-weight: 500;
					font-size: 30rpx;
					color: #333333;
					line-height: 30rpx;
					margin-left: 10rpx;
				}
			}

			.repast-way {
				display: flex;
				border-bottom: 1rpx solid rgba(153, 153, 153, 0.16);
				padding-bottom: 39rpx;
				flex-wrap: wrap;
				text-align: center;

				.repast-way-active {
					margin: 46rpx 5rpx 0 5rpx;
					padding: 10rpx 0;
					background: rgba(30, 97,246,0.5);
					border-radius: 37rpx;
					border: 1rpx solid #1B5FF5;
				}

				.repast-way-item {
					margin: 46rpx 5rpx 0 5rpx;
					padding: 10rpx 0;
					background: #FFFFFF;
					border-radius: 37rpx;
					border: 1rpx solid #999999;
					color: #999999;
				}
			}

			.repast-contact {
				display: flex;
				justify-content: space-between;
				margin-top: 35rpx;

				.repast-contact-title {
					display: flex;
					line-height: 34rpx;
					font-family: Source Han Sans SC;
					font-weight: 500;
					font-size: 30rpx;
					color: #333333;

					image {
						width: 26rpx;
						height: 34rpx;
					}

					.contact-info {
						margin-left: 10rpx;
					}
				}

				.repast-contact-input {
					input {
						text-align: right;
						// width: 200rpx;
					}
				}
			}
		}

		.pickup {
			margin: 29rpx;
			padding: 64rpx 10rpx;
			background-color: #ffffff;
			border-radius: 10rpx;

			.requirebox {
				text-align: center;

				.require {
					color: #999999;
					margin: 15rpx 0;
				}
			}

			.ordercode {
				.ordercode-title {
					color: #1B5FF5;
					border-bottom: 1rpx solid #1B5FF5;
					display: inline;
					margin: 0 33rpx;
				}

				.shop-info {
					margin-top: 28rpx;

					.opera {
						padding-bottom: 20rpx;
						height: 60rpx;

						.orderBox {
							background-image: linear-gradient(to right, #5396FB, #1255F4);
							margin-top: 10rpx;
							margin-left: 33rpx;
							border-radius: 10rpx;
							color: #ffffff;
							padding: 10rpx;
							float: left;
						}

						.lookShot {
							float: right;
							border: 1rpx solid #999999;
							border-radius: 20rpx;
							color: #999999;
							padding: 10rpx 15rpx;
							margin-top: 10rpx;
							margin-right: 33rpx;
						}
					}

					.handlestart {
						display: flex;
						justify-content: end;
						color: #999999;
						margin-right: 18px;
					}

					.shop-item {
						display: flex;
						justify-content: space-between;
						margin: 15rpx 0;
						padding-left: 33rpx;

						.eating {
							display: flex;

							image {
								width: 50rpx;
								height: 50rpx;
							}

							.eating-name {
								color: #999999;
								margin-left: 15rpx;
								overflow: hidden;
								text-overflow: ellipsis;
							}
						}

						.orderNum {
							color: #999999;
							margin-right: 33rpx;
						}
					}
				}
			}
		}

		.mealsbox {
			background-repeat: no-repeat;
			background-size: 100%;
			margin: 0 29rpx;
			padding: 33rpx;
			padding-bottom: 200rpx;

			.orderTitle {
				font-size: 34rpx;
				color: #333333;
				font-weight: bold;
			}

			.bigTitle {
				font-size: 30rpx;
				color: #333333;
				font-weight: bold;
				font-family: Source Han Sans SC;
				margin: 15rpx 0;
			}

			.mealsbox-title {
				font-family: Source Han Sans SC;
				font-weight: 500;
				font-size: 32rpx;
				color: #1B5FF5;
			}

			.mealsbox-detail {
				display: flex;
				border-bottom: 1rpx solid rgba(153, 153, 153, 0.16);
				padding-bottom: 15rpx;
				margin-top: 20rpx;

				.mealsbox-detail-left {
					image {
						width: 149rpx;
						height: 105rpx;
					}
				}

				.mealsbox-detail-center {
					margin-left: 15rpx;

					.packageName {
						font-family: Source Han Sans SC;
						font-weight: 500;
						font-size: 30rpx;
						color: #333333;
					}

					.packageSub {
						font-family: Source Han Sans SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #1B5FF5;
						line-height: 40rpx;
						margin: 15rpx 0;

						.packageNum {
							margin-left: 30rpx;
						}
					}
				}

				.mealsbox-detail-right {
					display: flex;
					line-height: 35rpx;

					.mark {
						font-family: Source Han Sans SC;
						font-weight: 400;
						font-size: 20rpx;
						color: #333333;
					}
				}
			}

			.amount {
				display: flex;
				justify-content: flex-end;
				margin-top: 28rpx;
				line-height: 40rpx;

				.amount-num {
					font-family: Source Han Sans SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
					line-height: 40rpx;
				}

				.amount-mark {
					font-family: Source Han Sans SC;
					font-weight: 400;
					font-size: 20rpx;
					color: #333333;
				}
			}
		}

		.btn {
			background: #1B5FF5;
			box-shadow: 0rpx 9rpx 24rpx 0rpx rgba(168, 2, 0, 0.53);
			border-radius: 40rpx;
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			margin: 0 18rpx 59rpx 18rpx;
			padding: 27rpx 0;
			text-align: center;
			color: #ffffff;
		}
	}
</style>