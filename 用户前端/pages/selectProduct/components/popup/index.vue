<template>
	<view class="container" v-if="isShow">
		<!-- 黑色遮罩层 -->
		<view class="cover" @click.stop="closePopup">

		</view>
		<!-- 内容 -->
		<view class="probox">
			<block v-if="list.customization && list.customization.options.length>0">
				<view class="pro-img">
					<image :src="list.image" mode=""></image>
				</view>
				<view class="pro-name">
					{{list.name}}
				</view>
				<view class="pro-copies">
					<view class="pro-copies-item">
						第1份
					</view>
				</view>
				<scroll-view scroll-y="true" :style="'height:'+scrollHeight+'rpx;'">
					<view class="pro-selectoption">
						<block v-for="(item,index) in list.customization.options" :key="index">
							<view class="pro-boxoption">
								<view class="pro-select-title">
									{{item.name}}
								</view>
								<view class="multiple" style="justify-content: center;">
									<view class="multipleoption">
										<view class="multipleboxoption" @click="changeChecked(item,index)" v-if="item.price<1">
											<view :class="item.checked?'multiple-item-select':'multiple-item-noselect'" style="left: 0;">
												√
											</view>
											<view :class="item.checked?'multiple-item-img':'multiple-item-noimg'">
												<image :src="item.image" mode=""></image>
											</view>
											<view class="multiple-item-title">
												{{item.name}}
											</view>
										</view>
									</view>
								</view>
							</view>
						</block>
					</view>
				</scroll-view>
			</block>
			<block v-if="list.customization && list.customization.items.length>0">
				<view class="pro-img">
					<image :src="list.image" mode=""></image>
				</view>
				<view class="pro-copies">
					<view class="pro-copies-item">
						第1份
					</view>
				</view>
				<scroll-view scroll-y="true" :style="'height:'+scrollHeight+'rpx;'">
					<view class="pro-select">
						<block v-for="(item,itemIndex) in list.customization.items" :key="itemIndex">
							<view class="pro-box">
								<view class="pro-select-title" v-if="dealDate(item.values)">
									{{item.name}}
								</view>
								<view class="multiple">
									<view class="multiple-item">
										<block v-for="(ite,idx) in item.values" :key="idx">
											<view class="multiplebox" @click="changeChecked(ite,idx,itemIndex)" v-if="ite.price<1">
												<view :class="ite.checked?'multiple-item-select':'multiple-item-noselect'">
													√
												</view>
												<view :class="ite.checked?'multiple-item-img':'multiple-item-noimg'">
													<image :src="ite.image" mode=""></image>
												</view>
												<view class="multiple-item-title">
													{{ite.name}}
												</view>
											</view>
										</block>
										<!-- <block v-else>
										<view class="multiple-item-noselect">
											√
										</view>
										<view class="multiple-item-noimg">
											<image src="../../../../static/logo.png" mode=""></image>
										</view>
									</block> -->
									</view>
								</view>
							</view>
						</block>
					</view>
				</scroll-view>
			</block>
			<view class="btnbox">
				<view class="btn" @click="selectedOk">
					选好了
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isShow: false,
				scrollHeight: '',
			}
		},
		onShow() {

		},
		mounted() {
			this.getHeight()
		},
		methods: {
			// 打开弹出层
			changeShow() {
				this.isShow = !this.isShow
			},
			// 关闭弹出层
			closePopup() {
				this.isShow = false
			},
			// 选择完毕
			selectedOk() {
				this.isShow = false
				this.$emit('changeSpeac', this.list)
			},
			changeChecked(item, index, itemIndex) {
				if (this.list.customization.options.length) {
					this.list.customization.options.forEach((option, idx) => {
						if (index === idx) {
							option.checked= option.checked == 1? 0:1
						}
					})
				}
				if (this.list.customization.items.length) {
					this.list.customization.items[itemIndex].values.forEach((option, idx) => {
						option.checked = 0
					})
					this.list.customization.items[itemIndex].values[index].checked = 1
				}
			},
			getHeight() {
				this.scrollHeight = uni.getSystemInfoSync().windowHeight - 115
			},
			dealDate(values){
				return values.some((value) => value.price < 1);
			}
		},
		computed: {
			list() {
				return this.$store.state.popUpList
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		z-index: 999;

		.cover {
			background-color: black;
			opacity: 0.6;
			position: fixed;
			top: 0;
			right: 0;
			left: 0;
			bottom: 0;
			height: 100vh;
		}

		.probox {
			background-color: #ffffff;
			border-top-left-radius: 20rpx;
			border-top-right-radius: 20rpx;
			height: 70vh;
			z-index: 99;
			position: fixed;
			bottom: 0;
			right: 0;
			left: 0;

			.pro-img {
				margin-top: -75rpx;
				display: flex;
				justify-content: center;

				image {
					width: 150rpx;
					height: 150rpx;
				}
			}

			.pro-name {
				text-align: center;
				font-size: 38rpx;
				color: #333333;
				font-weight: bold;
				font-family: Source Han Sans SC;
				margin: 30rpx 0;
			}

			.pro-copies {
				display: flex;
				justify-content: center;

				.pro-copies-item {
					padding: 10rpx 20rpx;
					background-color: #1B5FF5;
					border-radius: 10rpx;
					margin: 5rpx;
					color: #fff;
				}
			}

			.pro-selectoption {
				padding: 20rpx;
				display: flex;
				flex-wrap: wrap;
			}

			.pro-select {
				padding: 20rpx;
				// display: flex;
				// flex-wrap: wrap;
				justify-content: center;
			}

			.pro-select,
			.pro-selectoption {
				.pro-boxoption {
					width: calc(100% / 3);
					text-align: center;
				}

				.pro-box {
					text-align: center;
				}

				.pro-box,
				.pro-boxoption {
					.pro-select-title {
						font-weight: 400;
						font-size: 13px;
						color: #999999;
					}

					.multiple-item-title {
						margin-top: 10rpx;
						text-align: center;
					}

					.multipleoption {
						justify-content: center;
					}

					.multiple {
						justify-content: space-between;
					}

					.multiple,
					.multipleoption {
						display: flex;
						flex-wrap: wrap;

						.multipleoption {
							margin-top: 15rpx;
							display: flex;
							flex-wrap: wrap;
						}

						.multiple-item {
							margin-top: 15rpx;
							display: flex;
							flex-wrap: wrap;
							width: 100%;
						}

						.multiple-item,
						.multipleoption {

							.multiplebox {
								width: calc(100% / 3);
							}

							.multiplebox,
							.multipleboxoption {
								position: relative;

								.multiple-item-select {
									background-color: #1B5FF5;
									width: 45rpx;
									height: 45rpx;
									border-radius: 50%;
									color: #fff;
									text-align: center;
									position: absolute;
									left: 35rpx;
								}

								.multiple-item-noselect {
									display: none;
								}

								.multiple-item-noimg {
									border-radius: 50%;
									display: flex;
									justify-content: center;
									align-items: center;
									width: 150rpx;
									height: 150rpx;
									margin: auto;
									border: 1rpx solid #999999;

									image {
										width: 100rpx;
										height: 100rpx;
										border-radius: 50%;
									}
								}

								.multiple-item-img {
									border-radius: 50%;
									border: 1rpx solid #1B5FF5;
									display: flex;
									justify-content: center;
									align-items: center;
									width: 150rpx;
									height: 150rpx;
									margin: auto;

									image {
										width: 100rpx;
										height: 100rpx;
										border-radius: 50%;
									}
								}
							}
						}
					}
				}
			}

			.btnbox {
				.btn {
					position: fixed;
					bottom: 10rpx;
					left: 0;
					right: 0;
					background-color: #1B5FF5;
					border-radius: 40rpx;
					height: 80rpx;
					color: #fff;
					line-height: 80rpx;
					text-align: center;
					margin: 0 20rpx;
				}
			}
		}

	}
</style>