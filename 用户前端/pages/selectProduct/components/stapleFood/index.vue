<template>
	<view class="container">
		<view class="staple">
			<span class="staple-name">{{productLists.name}}</span>
			<block v-for="(itemOne,indexOne) in productLists.comboItems" :key="indexOne">
				<!-- {{itemOne.className}} -->
				<view class="title">
					{{itemOne.className}}(可选{{itemOne.quantity}}件)
				</view>
				<view class="select">
					<view class="" v-for="(itemTwo,indexTwo) in itemOne.comboProducts" :key="indexTwo"
						:class="itemTwo.isDefault && itemTwo.quantity>=1 ?'selectboxActive':'selectbox'">
						<!-- <block v-if="itemTwo.diffPriceText==0"> -->
						<view @click="oneQuantity(itemOne,indexOne,indexTwo)">
							<view class="select-img">
								<image :src="itemTwo.image" mode="" style="width: 84px;height:64px;"></image>
							</view>
							<view class="select-name">
								{{itemTwo.name}}
							</view>				
							<view class="seletnum"  v-if="itemOne.quantity>1">
								<block v-if="itemTwo.isDefault">
									<view class="seletnum-item" @click="choiceSelf(indexOne,indexTwo,'reduce')">
										<image src="@/static/images/pickingMeals/jian-ok.png" mode="" class="seletnum-item-image"></image>
									</view>
									<view class="">
										{{itemTwo.quantity}}
									</view>
								</block>
								<block v-else>
									<view class="seletnum-item">
									</view>
									<view class="">
									</view>
								</block>
								<block v-if="itemTwo.isDefault || !isCheckLength(indexOne)">
									<view class="seletnum-item" @click="choiceSelf(indexOne,indexTwo,'add')">
										<image src="@/static/images/pickingMeals/jia-ok.png" mode="" class="seletnum-item-image"></image>
									</view>
								</block>
							</view>
<!-- 							<view class="select-order" @click="openPopup(itemTwo)"
								v-if="itemTwo && itemTwo.customization && itemTwo.customization.options && (itemTwo.hasCustomization === 1 && (itemTwo.customization.options.length > 0 || (itemTwo.customization.items && itemTwo.customization.items.length > 0))) && itemTwo.isDefault">
								定制
							</view> -->
							<view class="select-order" @click="openPopup(itemTwo)"
								v-if="itemTwo && itemTwo.hasCustomization === 1 && itemTwo.isDefault">
								定制
							</view>
						</view>
						<!-- </block> -->
					</view>
				</view>
			</block>
		</view>
		<!-- 弹出层 -->
		<popup ref="changeShow" :customizedData='customizedData' @changeSpeac='receiveSpeac'></popup>
	</view>
</template>

<script>
	import popup from '../popup/index.vue'
	import {
		customerInfo
	} from '@/api/request.js'
	import {
		createLogger
	} from 'vuex'
	export default {
		props: ['inde',],
		components: {
			popup
		},
		data() {
			return {
				customizedData: [],
				cuParams: {
					productCode: '',
					storeCode: ''
				},
				produceParams: [],
				changeItems: {},
				localPackageAllList: [],
			}
		},
		mounted() {

		},
		methods: {
			oneQuantity(itemOne,indexOne,indexTwo){
				if(itemOne.quantity==1){
					const packageAllList=this.$store.state.packageAllList[this.inde].comboItems[indexOne]
					if(packageAllList.quantity==1){
						packageAllList.comboProducts.forEach((item,index)=>{
							if(index === indexTwo){
								item.isDefault=1
							}else{
								item.isDefault=0
							}
						})
					}
					if(packageAllList.quantity>1){
						const comboProducts=packageAllList.comboProducts[indexTwo]
						if(comboProducts.isDefault){
							comboProducts.isDefault=0
						}else{
							const checkedCount = packageAllList.comboProducts.filter(s => s.isDefault==1).length;
							if(checkedCount<packageAllList.quantity){
								comboProducts.isDefault=1
							}
						}
					}
				}
			},
			isCheckLength(indexOne) {
				let packageAllList = this.$store.state.packageAllList[this.inde].comboItems[indexOne]
				let comboProducts = packageAllList.comboProducts
				let totalCount = packageAllList.comboProducts.filter(s => s.isDefault).reduce((sum, s) => sum + s.quantity, 0)
				return totalCount === packageAllList.quantity
			},
			choiceSelf(indexOne, indexTwo, type) {
				
				let packageAllList = this.$store.state.packageAllList[this.inde].comboItems[indexOne]
				let comboProducts = packageAllList.comboProducts[indexTwo]
				let isDeafultCount = packageAllList.comboProducts.filter(s => s.isDefault).length
				let totalCount = packageAllList.comboProducts.filter(s => s.isDefault).reduce((sum, s) => sum + s.quantity, 0)
				if (false) {//packageAllList.quantity === packageAllList.comboProducts.length
					return
				} else {
					if (type === 'reduce') {
						if (comboProducts.quantity > 1) {
							comboProducts.quantity--
						} else if (comboProducts.quantity === 1) {
							comboProducts.isDefault =0
							comboProducts.quantity = 0
						}
					} else if (type === 'add') {
						if (!comboProducts.isDefault) {
							if (isDeafultCount < packageAllList.quantity && totalCount < packageAllList.quantity) {
								comboProducts.isDefault = 1
								// comboProducts.quantity = 1
							} else {
								uni.showToast({
									title: '已经选够啦~'
								})
							}
						} else {
							if (comboProducts.quantity < packageAllList.quantity && totalCount < packageAllList.quantity) {
								comboProducts.quantity++
								this.$forceUpdate();
							} else {
								uni.showToast({
									title: '已经选够啦~',
									icon: 'none'
								})
							}
						}
					}
				}
			},
			//quantity
			// const packageAllList=this.$store.state.packageAllList[this.inde].productLists[this.secIndex].comboItems[indexOne]
			// if(packageAllList.quantity==1){
			// 	packageAllList.comboProducts.forEach((item,index)=>{
			// 		item.isDefault = index === indexTwo;
			// 	})
			// }
			// if(packageAllList.quantity>1){
			// 	const comboProducts=packageAllList.comboProducts[indexTwo]
			// 	if(comboProducts.isDefault){
			// 		comboProducts.isDefault=0
			// 	}else{
			// 		const checkedCount = packageAllList.comboProducts.filter(s => s.isDefault==1).length;
			// 		if(checkedCount<packageAllList.quantity){
			// 			comboProducts.isDefault=1
			// 		}
			// 	}
			// }
			openPopup(customization) {
				this.customizedData = customization
				this.$refs.changeShow.changeShow()
				this.cuParams.productCode = customization.code
				this.cuParams.storeCode = this.$store.state.storeCode.code
				this.customerInfo()
			},
			// 获取定制信息
			async customerInfo() {
				let res = await customerInfo(this.cuParams)
				this.$store.commit('updatepopUpList', res.data)
			},
			receiveSpeac(list) {
				this.$store.state.packageAllList.forEach((packItem) => {
						if ((packItem.type === 7 || packItem.type === 2) && packItem.hasOwnProperty('comboItems')==true) {
							const comboItems = packItem.comboItems
							comboItems.forEach(comItem => {
								const comboProducts = comItem.comboProducts
								comboProducts.forEach(compItem => {
									if (compItem.code === list.code) {
										compItem.customization = list.customization
									}
								})
							})
						}
				})
				console.log(this.$store.state.packageAllList,'22222222');
			},
		},
		computed: {
			productLists() {
				return this.$store.state.packageAllList[this.inde]
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 29rpx 0;

		.staple {
			border-bottom: 1rpx solid rgba(153, 153, 153, 0.15);
			padding-bottom: 34rpx;

			.staple-name {
				font-size: 30rpx;
				font-weight: 500;
			}

			.title {
				font-family: Source Han Sans SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				margin: 15rpx 0;
			}

			.select {
				display: flex;
				flex-wrap: wrap;

				.selectboxActive {
					width: calc((100% - 100rpx)/3);
					border: 1px solid #1B5FF5;
					border-radius: 20rpx;
					padding: 10rpx;
					margin: 15rpx 5rpx 0 5rpx;
					min-height: 300rpx;

					.select-img {
						margin-top: 35rpx;
						margin-bottom: 15rpx;
						display: flex;
						justify-content: center;

						image {
							width: 133rpx;
							height: 90rpx;
						}
					}

					.select-name {
						font-family: Source Han Sans SC;
						font-weight: bold;
						font-size: 24rpx;
						color: #333333;
						text-align: center;
					}

					.seletnum {
						display: flex;
						margin: 25rpx 0;
						height: 45rpx;

						.seletnum-item {
							flex: 1;
							text-align: center;

							.seletnum-item-image {
								width: 45rpx;
								height: 45rpx;
							}
						}
					}

					.select-order {
						margin: auto;
						width: 135rpx;
						height: 48rpx;
						line-height: 48rpx;
						background: #1B5FF5;
						border-radius: 10rpx;
						border: 1px solid #1B5FF5;
						color: #ffffff;
						text-align: center;
						margin-top: 25rpx;
						font-size: 24rpx;
					}
				}

				.selectbox {
					width: calc((100% - 100rpx)/3);
					padding: 10rpx;
					margin: 15rpx 5rpx 0 5rpx;
					min-height: 300rpx;
					.select-img {
						margin-top: 50rpx;
						margin-bottom: 15rpx;
						display: flex;
						justify-content: center;

						image {
							width: 133rpx;
							height: 90rpx;
						}
					}

					.select-name {
						font-family: Source Han Sans SC;
						font-weight: bold;
						font-size: 24rpx;
						color: #333333;
						text-align: center;
					}

					.seletnum {
						display: flex;
						margin: 25rpx 0;

						.seletnum-item {
							flex: 1;
							text-align: center;

							.seletnum-item-image {
								width: 45rpx;
								height: 45rpx;
							}
						}
					}

					.select-order {
						margin: auto;
						width: 135rpx;
						height: 48rpx;
						line-height: 48rpx;
						background: #1B5FF5;
						border-radius: 10rpx;
						border: 1px solid #1B5FF5;
						color: #ffffff;
						text-align: center;
						margin-top: 25rpx;
						font-size: 24rpx;
					}
				}
			}
		}

		.other {
			border-bottom: 1rpx solid rgba(153, 153, 153, 0.15);
			padding-bottom: 34rpx;

			.title {
				font-family: Source Han Sans SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				margin-bottom: 15rpx;
			}

			.kind {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.kindActive {
					margin-top: 30rpx;
					width: calc((100% - 200rpx)/3);
					border: 2rpx solid #1B5FF5;
					border-radius: 20rpx;
					padding: 21rpx;
				}

				.kibdbox {
					margin-top: 30rpx;
					width: calc((100% - 200rpx)/3);
					border-radius: 20rpx;
					padding: 21rpx;
				}

				.kibdbox,
				.kindActive {
					.kind-top {
						display: flex;
						justify-content: space-between;

						image {
							width: 42rpx;
							height: 42rpx;
						}

						.kind-top-right {
							display: flex;

							.add,
							.sign,
							.money {
								font-family: Source Han Sans SC;
								font-weight: bold;
								font-size: 24rpx;
								color: #333333;
							}
						}
					}

					.kind-center {
						width: 152rpx;
						height: 158rpx;
						margin-top: 25rpx;

						image {
							width: 100%;
							height: 100%;
						}
					}

					.kind-bottom {
						font-family: Source Han Sans SC;
						font-weight: bold;
						font-size: 22rpx;
						color: #333333;
						text-align: center;
						margin-top: 10rpx;
					}
				}
			}
		}
	}
</style>