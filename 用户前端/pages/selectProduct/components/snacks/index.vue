<template>
	<view class="container">
		<view class="staple">
			<block>
				<view class="title">
					{{productLists.longName}}
				</view>
					<view class="select">
						<view class="selectbox">
							<view class="select-img">
								<image :src="productLists.image" mode=""></image>
							</view>
							<view class="select-name">
								{{productLists.name}}
							</view>
							<view class="select-order" @click="openPopup(productLists)" v-if="productLists && (productLists.customizationMode===1 && (productLists.customization.options.length>0 || productLists.customization.items.length>0))">
								定制
							</view>			
						</view>
					</view>
			</block>
		</view>
		<!-- 弹出层 -->
		<popup ref="changeShow" :customizedData='customizedData' @changeSpeac='receiveSpeac'></popup>
	</view>
</template>

<script>
	import popup from '../popup/index.vue'
	import {customerInfo} from '@/api/request.js'
	export default {
		props: ['productLists'],
		components: {
			popup
		},
		data() {
			return {
				customizedData: [],
				cuParams:{
					productCode:'',
					storeCode:''
				}
			}
		},
		mounted() {
			
		},
		onLoad() {},
		methods: {
			openPopup(customization) {
				this.customizedData = customization
				this.$store.commit('updatepopUpList',this.customizedData)
				this.$refs.changeShow.changeShow()
				this.cuParams.productCode=customization.code
				this.cuParams.storeCode=this.$store.state.storeCode.code
				this.customerInfo()
			},
			// 获取定制信息
			async customerInfo(){
				let res=await customerInfo(this.cuParams)
				this.$store.commit('updatepopUpList',res.data)
			},
			receiveSpeac(list) {
				this.$store.state.packageAllList.forEach((packItem)=>{
						if(packItem.code===list.code && packItem.type===1){
							packItem.customization=list.customization
						}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 29rpx 0;

		.staple {
			border-bottom: 1rpx solid rgba(153, 153, 153, 0.15);
			padding-bottom: 34rpx;

			.title {
				font-family: Source Han Sans SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				margin-bottom: 15rpx;
			}

			.select {
				display: flex;
				flex-wrap: wrap;

				.selectbox {
					width: calc(100% / 3);

					.select-img {
						margin-top: 50rpx;
						margin-bottom: 15rpx;
						display: flex;
						justify-content: center;

						image {
							width: 133rpx;
							height: 90rpx;
						}
					}

					.select-name {
						font-family: Source Han Sans SC;
						font-weight: bold;
						font-size: 24rpx;
						color: #333333;
						text-align: center;
					}

					.select-order {
						margin: auto;
						width: 135rpx;
						height: 48rpx;
						line-height: 48rpx;
						background: #1B5FF5;
						border-radius: 10rpx;
						border: 1px solid #1B5FF5;
						color: #ffffff;
						text-align: center;
						margin-top: 25rpx;
						font-size: 24rpx;
					}
				}
			}
		}

		.other {
			border-bottom: 1rpx solid rgba(153, 153, 153, 0.15);
			padding-bottom: 34rpx;

			.title {
				font-family: Source Han Sans SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				margin-bottom: 15rpx;
			}

			.kind {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.kindActive {
					margin-top: 30rpx;
					width: calc((100% - 200rpx)/3);
					border: 2rpx solid #1B5FF5;
					border-radius: 20rpx;
					padding: 21rpx;
				}

				.kibdbox {
					margin-top: 30rpx;
					width: calc((100% - 200rpx)/3);
					border-radius: 20rpx;
					padding: 21rpx;
				}

				.kibdbox,
				.kindActive {
					.kind-top {
						display: flex;
						justify-content: space-between;

						image {
							width: 42rpx;
							height: 42rpx;
						}

						.kind-top-right {
							display: flex;

							.add,
							.sign,
							.money {
								font-family: Source Han Sans SC;
								font-weight: bold;
								font-size: 24rpx;
								color: #333333;
							}
						}
					}

					.kind-center {
						width: 152rpx;
						height: 158rpx;
						margin-top: 25rpx;

						image {
							width: 100%;
							height: 100%;
						}
					}

					.kind-bottom {
						font-family: Source Han Sans SC;
						font-weight: bold;
						font-size: 22rpx;
						color: #333333;
						text-align: center;
						margin-top: 10rpx;
					}
				}
			}
		}
	}
</style>