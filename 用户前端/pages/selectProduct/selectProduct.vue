<template>
	<view>
		<view class="container" v-if="isShow==true">
			<view class="texts_box">
				<view class="texts" v-if="optionSelectNum===1">
					本链接有<span style="font-size: 28rpx;">{{optionSelectNum}}</span>件可选，请选择其中一件进行兑换！
				</view>
				<view class="texts" v-else>
					本链接有<span style="font-size: 28rpx;">{{optionSelectNum}}</span>件商品可进行兑换！
				</view>
			</view>
			<!-- @click="selectOrder(index)" -->
			<view class="scrollbox">
				<view v-for="(item,index) in proLists" :key="index" :class="item.ischecked===true?'orderActive':'bugprobox'">
					<view class="bugpro">
						<view class="bugpro-img">
							<image :src="item.IMAGE" mode=""></image>
						</view>
						<view class="bugpro-content">
							<view class="bugpro-content-top">
								{{item.TITLE}}
							</view>
						</view>
						<view class="bugpro-exchange" v-if="optionSelectNum==1" @click="execOne(index)">
							去兑换
						</view>
					</view>
				</view>
			</view>
			<view class="nextBtn" @click="goExecDetail()" v-if="optionSelectNum!==1">
				下一步
			</view>
		</view>
		<view class="" v-else-if='isShow==false' style="background-image: url('../../static/images/product/background.png');background-repeat: no-repeat;background-size: 100%;height: 229rpx;display: flex;align-items: center;color: #ffffff;padding-left: 30px;">
			暂无数据
		</view>
	</view>
</template>

<script>
	import {
		createLogger
	} from 'vuex'
	import {
		packageDetail,
		productDetail
	} from '@/api/request.js'
	import checkCodeStatus from '../../mixin/checkCodeStatus'
	export default {
		mixins:[checkCodeStatus],
		data() {
			return {
				proLists: {},
				optionSelectNum: '',
				ischeckedCount: 0,
				haveCountParams: {},
				nowTimesStamp: '',
				isShow:null,
				code:''
			}
		},
		onShow() {
			this.compareTimes()
			this.packageDetail()
		},
		onLoad(e) {
			this.code=e.s
		},
		methods: {
			compareTimes() {
				// 获取当前时间
				let now = new Date();
				const currentHours = String(now.getHours()).padStart(2, '0');
				const currentMinutes = String(now.getMinutes()).padStart(2, '0');
				const currentSeconds = String(now.getSeconds()).padStart(2, '0');
				this.nowTimesStamp = `${currentHours}:${currentMinutes}:${currentSeconds}`;
			},
			async packageDetail() {
				let res = await packageDetail({code:this.codkey})
				if(res.code==200){
					this.isShow=true
					this.optionSelectNum = res.anum
					this.proLists = res.data
					this.$store.commit('updateProLists', this.proLists)
					this.$store.state.exchangeParams.id = res.data.map(item => {
						return {
							id:item.ID,
						}
					})
					this.$store.state.exchangeParams.storeCode=this.storeCode
					// 营业时间转换为时间戳
					let START_TIME = this.proLists[0].START_TIME
					let END_TIME = this.proLists[0].END_TIME
					if (this.nowTimesStamp < START_TIME || this.nowTimesStamp >= END_TIME) {
						uni.showModal({
							content: '当前套餐不在营业时间内，请稍后再试！',
							showCancel: false,
							success: (res) => {
								uni.navigateBack({
									delta: 1
								})
							}
						})
					}
				}else{
					this.isShow=false
				}
				// 只在首次加载时重置选择状态，避免用户返回时丢失选择
			if (!this.$store.state.proLists || this.$store.state.proLists.length === 0) {
				this.proLists.forEach(item => {
					item.ischecked = false
				})
			} else {
				// 恢复之前的选择状态
				this.proLists = this.$store.state.proLists
			}
			},
			goExecDetail() {
				// 所有套餐全部兑换
				if (this.optionSelectNum === this.proLists.length) {
					this.haveCountParams.id = this.proLists.map((item) => {
						return {
							id:item.ID
						}
					})
					this.haveCountParams.storeCode=this.storeCode
					this.$store.commit('updatehaveCountParams', this.haveCountParams)
				}
				if (this.haveCountParams.id.length > 0) {
					uni.navigateTo({
						url: './selectProductDetail?s='+this.code
					})
				} else {
					uni.showToast({
						title: '请选择商品',
						icon: 'none'
					})
				}
			},
			// 一个商品兑换
			execOne(index) {
				if (this.optionSelectNum === 1) {
					this.haveCountParams.id = [{
						id: this.proLists[index].ID
					}]
					this.haveCountParams.storeCode=this.storeCode
					this.$store.commit('updatehaveCountParams', this.haveCountParams)
					uni.navigateTo({
						url: './selectProductDetail?s='+this.code
					})
				}
			},
			// selectOrder(index) {
			// 	if (this.ischeckedCount === this.optionSelectNum && !this.proLists[index].ischecked) {
			// 		uni.showToast({
			// 			title:'请先取消其中一个的选择，再选择其他商品。',
			// 			icon:'none'
			// 		})
			// 		return
			// 	}
			// 	this.proLists[index].ischecked = !this.proLists[index].ischecked;
			// 	if (this.proLists[index].ischecked) {
			// 		if (this.ischeckedCount < this.optionSelectNum) {
			// 			this.ischeckedCount++;
			// 		}
			// 		if (this.ischeckedCount === this.optionSelectNum) {
			// 			this.haveCountParams= this.proLists.filter(item => item.ischecked).map(item => ({
			// 				title: item.title,
			// 				discount: item.discount,
			// 				positiveprice: item.positiveprice,
			// 			}));
			// 		}
			// 	} else {
			// 		this.ischeckedCount--;
			// 	}
			// }
		},
		computed: {
			codkey() {
				return this.$store.state.codeId
			},
			storeCode() {
				return this.$store.state.storeCode.code
			}
		}
	}
</script>
<style lang="scss" scoped>
	.texts_box{
		width: 700rpx;
		margin: auto;
		margin-top: 20rpx;
		line-height: 60rpx;
		font-size: 20rpx;
		text-align: center;
		background: linear-gradient(to right, #094CEF, #488DFA);
		border-radius: 50rpx;
		color: #fff;
		font-weight: 700;
	}
	.container {
		background-color: rgba(248, 247, 248, 1);
		height: 100vh;
		position: relative;
		overflow: hidden;

		.probg {
			background-repeat: no-repeat;
			background-size: 100%;
			height: 229rpx;
			display: flex;
			align-items: center;

			.protitle {
				font-family: Source Han Sans SC;
				font-weight: 400;
				font-size: 30rpx;
				color: #1296db;
				padding-left: 55rpx;
				width: 47vw;
				word-wrap: break-word;
				line-height: 50rpx;
				span {
					color: #1B5FF5;
					font-size: 40rpx;
					font-weight: 400;
				}
			}
		}
		.scrollbox{
			overflow-y: auto;
			height: calc(100vh - 229rpx - 80rpx);
		.orderActive {
			background-color: red;
			margin: 49rpx 31rpx 27rpx 31rpx;
			border-radius: 20rpx;
			padding: 36rpx;
		}

		.bugprobox {
			background-color: #FFFFFF;
			margin: 49rpx 31rpx 27rpx 31rpx;
			border-radius: 20rpx;
			padding: 36rpx;
		}

		.bugprobox,
		.orderActive {
			.bugpro {
				display: flex;
				justify-content: space-between;

				.bugpro-img {
					image {
						width: 127rpx;
						height: 135rpx;
					}
				}

				.bugpro-content {
					flex: 1;
					margin: 0 18rpx;
					width: calc(100% - 400rpx);

					.bugpro-content-top {
						height: 100%;
						width: 100%;
						line-height: 135rpx;
						// font-family: Source Han Sans SC;
						// font-weight: 500;
						// font-size: 34rpx;
						// color: #333333;
						// white-space: nowrap;
						// overflow: hidden;
						// text-overflow: ellipsis;
					}
				}

				.bugpro-exchange {
					background-color: #1B5FF5;
					border-radius: 50rpx;
					height: 60rpx;
					padding: 5rpx 30rpx;
					line-height: 60rpx;
					color: #ffffff;
					margin-top: 40rpx;
				}
			}
		}
		}
		.nextBtn {
			background-color: #1B5FF5;
			border-radius: 50rpx;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			color: #ffffff;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			margin: 10rpx;
		}
	}
</style>