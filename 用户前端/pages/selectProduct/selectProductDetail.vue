<template>
	<view class="container" v-if="pageShow==true">
		<!-- 产品详情 -->
		<block v-if="checkArray && isDate">
			<view class="probg" style="background-image: url('../../static/images/product/background.png');">
				<view class="protitle">
					商品已售空，请换家门店试试~
				</view>
			</view>
		</block>
		<block v-else>
			<!-- 背景图 -->
			<!-- 			<view class="bg">

			</view> -->
			<view class="productbox">
				<view class="product" v-for="(item,index) in packageAllList" :key="index">
					<view class="title">
						<view class="title-big" @longpress="showDebugMenu" @touchstart="debugTouchStart" @touchend="debugTouchEnd">
							{{item.name}}
						</view>
						<!-- 						<view class="title-small">
							集麦当劳三大粥王奧秘于一碗，精选东北大米，星级水米比例12:
							1,悉心熬煮95分钟，直到粒粒开花。浓稠软糯好粥底，搭配细嫩鸡肉和鲜美雪菜笋丝，顺滑爽口，暖心暖胃。美味原味板烧鸡腿麦满分搭配精选优质鲜煮咖啡和脆薯饼，拿下今天从早餐开始!
						</view> -->
					</view>
					<block>
						<view class="scrollbox" @touchmove.stop="">
							
							<!-- <block v-for="(proOne,indexOne) in item.productLists" :key="indexOne"> -->
							<block v-if="item.type===1 || (item.type===7 && item.hasOwnProperty('comboItems')==false)">
								
								<snacks :productLists='item'></snacks>
							</block>
							<block v-else-if="item.type===3">
								
								<threeCom :productLists='item' :inde="index"></threeCom>
							</block>
							<block v-else>
								{{item.type}}
								<stapleFood :inde="index"></stapleFood>
							</block>
							<!-- </block> -->
							<!-- 		<scroll-view scroll-y="true" :style="'height:'+scrollHeight+'rpx;'">
								<block v-for="(proOne,indexOne) in item.productLists" :key="indexOne">
									<block v-for="(preTwp,indexTwo) in proOne.comboItems" :key="indexTwo">
										<block v-for="(preFour,indexFour) in preTwp.comboProducts" :key="indexFour">
											<stapleFood :productLists='preFour'></stapleFood>
										</block>
									</block>
								</block>
								</scroll-view> -->
						</view>
					</block>
				</view>
			</view>
			<view class="exchange">
				<view class="exchange-btn" @click="rightNowExec()">
					兑换
				</view>
			</view>
		</block>
	</view>
	<view class="" v-else-if="pageShow==false"
		style="background-image: url('../../static/images/product/background.png');background-repeat: no-repeat;background-size: 100%;height: 229rpx;display: flex;align-items: center;color: #ffffff;padding-left: 30px;">
		当前门店暂无选择的商品，请选择其他门店兑换！！！
	</view>
</template>

<script>
	import stapleFood from './components/stapleFood/index.vue'
	import snacks from './components/snacks/index.vue'
	import threeCom from './components/threeCom/index.vue'
	import checkCodeStatus from '../../mixin/checkCodeStatus'
	import {
		productDetail
	} from '@/api/request.js'
	export default {
		mixins: [checkCodeStatus],
		components: {
			stapleFood,
			snacks,
			threeCom
		},
		data() {
			return {
				scrollHeight: '',
				packageAllList: [],
				pageShow: null,
				isProductSoldOut: false,
				isDate: true,
				code: '',
				// 调试相关
				debugTouchStartTime: null
			}
		},
		onShow() {
			// 从store中恢复已有的商品选择状态，避免重置用户选择
			if (this.$store.state.packageAllList && this.$store.state.packageAllList.length > 0) {
				this.packageAllList = this.$store.state.packageAllList
				this.pageShow = true
			} else {
				this.packageAllList = []
				this.productDetail()
			}
			this.getHeight()
		},
		onLoad(e) {
			this.code = e.s
		},
		methods: {
			getHeight() {
				this.scrollHeight = uni.getSystemInfoSync().windowHeight - 115
			},
			async productDetail() {
				let res = await productDetail(this.haveCountParams)
				if (res.code == 200) {
					let arr = res.data.forEach(item => {
						item.POSITIVE.forEach(typeItem => {
							this.packageAllList.push(typeItem)
						})
						item.TICKET.forEach(typeItem => {
							this.packageAllList.push(typeItem)
						})
					})

					this.$store.commit('updatepackageParams',res.data)
					this.packageAllList.forEach(proItem => {
						if (proItem) {
							this.isDate = true
							this.pageShow = true
						} else {
							uni.showModal({
								content: '商品已售空，换家门店试试~',
								showCancel: false,
								success: (res) => {
									uni.navigateBack({
										delta: 1
									})
								}
							})
						}
						if (proItem.type !== 1 && proItem.type !== 3) {
							if (proItem.hasOwnProperty('comboItems') == true) {
								proItem.comboItems.forEach(comItem => {
									comItem.comboProducts.forEach(combItem => {
										// const match = combItem.diffPriceText.match(/\d+(\.\d+)?/);
										// combItem.diffPriceText = match ? match[0] : combItem.diffPriceText
										combItem.quantity = 1
									})
									// item.diffPriceText == 0 && item.isChecked
									// comItem.comboProducts = comItem.comboProducts.filter(item=>item.diffPriceText == 0)
									comItem.comboProducts = comItem.comboProducts.filter(item => {
										if (item.hasOwnProperty('isChecked') == false) {
											return item
										} else if (item.isChecked) {
											return item
										}
									})
								})
							}
						}
						if (proItem.type == 3) {
							proItem.specTypes.forEach(specItem => {
								specItem.specs.forEach(spsItem => {
									if (spsItem.hasOwnProperty('spread') == false) {
										spsItem.checked = 1
									}
								})
							})
						}
					})
					if (res.data.length == 0) {
						this.pageShow = false
					}
				} else {
					this.pageShow = false
				}
				this.$store.commit('updatePackageAllList', this.packageAllList)
				console.log(this.$store.state.packageAllList,'2');
			},
			rightNowExec() {
				// 详细日志记录 - 商品选择验证阶段
				const timestamp = new Date().toISOString()
				console.log(`[PRODUCT_DETAIL_SUBMIT] ${timestamp} 开始验证商品选择`)
				console.log(`[PRODUCT_DETAIL_DATA] ${timestamp} packageAllList:`, JSON.parse(JSON.stringify(this.packageAllList)))
				
				let count = []
				this.packageAllList.forEach((proItem, proIndex) => {
					console.log(`[PRODUCT_VALIDATION] ${timestamp} 验证商品 ${proIndex}:`, {
						code: proItem.code,
						name: proItem.name,
						type: proItem.type,
						hasComboItems: proItem.hasOwnProperty('comboItems')
					})
					
					if (proItem.type !== 1 && proItem.type !== 3 && proItem.hasOwnProperty('comboItems') == true) {
						proItem.comboItems.forEach((comItem, comIndex) => {
							const selectedCount = comItem.comboProducts.filter(combItem => combItem.isDefault).reduce((sum, s) => sum + s.quantity, 0)
							console.log(`[COMBO_VALIDATION] ${timestamp} 套餐项 ${proIndex}-${comIndex}:`, {
								comboName: comItem.name,
								requiredQuantity: comItem.quantity,
								selectedCount: selectedCount,
								comboProducts: comItem.comboProducts.map(cp => ({
									code: cp.code,
									name: cp.name,
									isDefault: cp.isDefault,
									quantity: cp.quantity
								}))
							})
							
							count.push({
								num: selectedCount,
								quantity: comItem.quantity,
								comboName: comItem.name
							})
						})
					}
				})
				
				console.log(`[VALIDATION_RESULT] ${timestamp} 验证结果:`, count)
				
				// 更新store状态
				this.$store.commit('updatePackageAllList', this.packageAllList)
				console.log(`[STORE_UPDATE] ${timestamp} 已更新store中的packageAllList`)
				
				// 保存详细日志到本地存储
				try {
					const detailLogs = uni.getStorageSync('product_detail_logs') || []
					detailLogs.push({
						timestamp,
						type: 'PRODUCT_SUBMIT_VALIDATION',
						packageAllList: JSON.parse(JSON.stringify(this.packageAllList)),
						validationCount: count,
						code: this.code
					})
					if (detailLogs.length > 50) {
						detailLogs.splice(0, detailLogs.length - 50)
					}
					uni.setStorageSync('product_detail_logs', detailLogs)
				} catch (e) {
					console.error('保存商品详情日志失败:', e)
				}
				
				if (count.every(item => item.num === item.quantity)) {
					console.log(`[NAVIGATION] ${timestamp} 验证通过，跳转到取餐页面`)
					uni.navigateTo({
						url: '/pages/pickingMeals/pickingMeals?s=' + this.code
					})
				} else {
					console.log(`[VALIDATION_FAILED] ${timestamp} 验证失败，商品未选齐`)
					uni.showModal({
						title: '您的餐品还未选齐~',
						showCancel: false
					})
				}
			},
			// 调试功能相关方法
			debugTouchStart() {
				this.debugTouchStartTime = Date.now()
			},
			debugTouchEnd() {
				this.debugTouchStartTime = null
			},
			showDebugMenu() {
				// 只在开发环境或特定条件下显示调试菜单
				if (process.env.NODE_ENV === 'development' || this.isDebugMode()) {
					uni.showActionSheet({
						itemList: ['日志查看器', '日志比对工具'],
						success: (res) => {
							if (res.tapIndex === 0) {
								// 跳转到日志查看器
								uni.navigateTo({
									url: '/pages/logViewer/logViewer'
								})
							} else if (res.tapIndex === 1) {
								// 跳转到日志比对工具
								uni.navigateTo({
									url: '/pages/logCompare/logCompare'
								})
							}
						}
					})
				}
			},
			isDebugMode() {
				// 检查是否为调试模式，可以通过多种方式判断
				// 1. 检查URL参数
				try {
					const pages = getCurrentPages()
					const currentPage = pages[pages.length - 1]
					if (currentPage && currentPage.options && currentPage.options.debug === 'true') {
						return true
					}
				} catch (e) {}
				
				// 2. 检查本地存储
				try {
					const debugFlag = uni.getStorageSync('debug_mode')
					if (debugFlag === 'true') {
						return true
					}
				} catch (e) {}
				
				// 3. 默认在开发环境下启用
				return process.env.NODE_ENV === 'development'
			},
		},
		computed: {
			haveCountParams() {
				return this.$store.state.haveCountParams
			},
			checkArray() {
				// return this.packageAllList.some(item => {
				// 	return Object.values(item).some(item => !item.productLists || item.productLists === '' || item
				// 		.productLists === null)
				// })

				return this.packageAllList.some((packageItem) => {
					if (packageItem.type == 7) {
						if (packageItem.hasOwnProperty('comboItems') == true) {
							const comboItems = packageItem.comboItems;
							return comboItems === '' || !comboItems || comboItems === null;
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;

		.probg {
			background-repeat: no-repeat;
			background-size: 100%;
			height: 229rpx;
			display: flex;
			align-items: center;

			.protitle {
				font-family: Source Han Sans SC;
				font-weight: 400;
				font-size: 30rpx;
				color: #FFFFFF;
				padding-left: 55rpx;
				width: 47vw;
				word-wrap: break-word;
				line-height: 50rpx;
			}
		}

		.bg {
			height: 363rpx;
			background-color: rgba(247, 186, 24, 1);
		}

		.product {
			-ms-overflow-style: none;
			scrollbar-width: none;
		}

		.productbox {
			overflow-y: auto;
			height: calc(100vh - 140rpx);

			.product {
				border-top-left-radius: 20rpx;
				border-top-right-radius: 20rpx;
				margin-top: -20rpx;
				background-color: #ffffff;
				padding: 34rpx;

				.title {
					border-bottom: 1rpx solid rgba(153, 153, 153, 0.15);

					.title-big {
						font-size: 38rpx;
						color: #333333;
						font-weight: bold;
						font-family: Source Han Sans SC;
						margin: 15rpx 0;
					}

					.title-small {
						font-family: Source Han Sans SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #999999;
						line-height: 37rpx;
						margin-bottom: 20rpx;
					}
				}

				.scrollbox {
					flex: 1; // 达到高度自适应
					overflow: scroll; // 关键代码，溢出的部分显示滚动条 不溢出也显示
					height: calc(100% - 300rpx);
				}
			}
		}

		.exchange {
			height: 140rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 9rpx 24rpx 0rpx rgba(168, 2, 0, 0.2);
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;

			.exchange-btn {
				height: 80rpx;
				background: #1B5FF5;
				box-shadow: 0rpx 9rpx 24rpx 0rpx rgba(30, 97, 246, 100);
				border-radius: 40rpx;
				color: #ffffff;
				text-align: center;
				line-height: 80rpx;
				margin: auto 0;
				width: 100%;
			}
		}
	}
</style>