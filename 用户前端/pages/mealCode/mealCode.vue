<template>
	<view class="content">
		<view class="Take" style="">
			<view style="display: flex;">
				<view class="code">{{mockList.orderStatusTitle?mockList.orderStatusTitle:'待出餐'}}</view>
				<image src="../../static/ui/12258763.jpg" style="width: 75rpx;height: 26rpx;margin-top: 40rpx;"></image>
			</view>
			<view class="ty">
				<image src="../../static/ui/1269863.jpg"
					style="width: 26rpx;height: 26rpx;vertical-align: middle;margin-right: 10rpx;"></image>
				{{mockList.orderStatusSubTitle}}
			</view>
		</view>
		<view class="manner" style="background-image: url('https://img.mcd.cn/cms/images/d62b57b55bd94493.png');">
			<view class="m-right">
				<image src="https://store-img.mcd.cn/store-pictures/1960113/1960113-d10f046be5744fa687c935e58fabbb7d.png"
					mode=""></image>
			</view>
		</view>
		<view class="info">
			<view class="shopInfo" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
				<image src="@/static/ui/D.jpg" style="width: 40rpx;height: 40rpx;vertical-align: middle;"></image>
				{{mockList.storeName}}
			</view>
			<view class="foodItem" v-for="(item,index) in mockList.orderProductList" :key="index">
				<view class="imgs">
					<image :src="item.productImage" style="width: 100%;height: 100%;" lazy-load></image>
				</view>
				<view class="foodTitle">
					<view class="foodName">{{item.productName}}</view>
					<view class="bei" style="color:#adadad;font-size:15px;" v-for="(items,indexs) in item.comboItemList" :key="indexs">
						{{items.name}} × {{items.quantity}}
					</view>
					<view class="foodName" style="font-size: 26rpx;font-weight: 500;">{{item.quantity}}份</view>
				</view>
			</view>
		</view>
		<view class="Oder">
			<view class="oderTitle" style="border-bottom: 1rpx solid #EDEDED;">
				订单信息
			</view>
			<view class="oderItem">
				<view class="oderLeft">订单号</view>
				<view class="oderRigft">{{mockList.orderId}}</view>
				<view class="" style="display: flex; align-items: center;margin-left:3px;">
					<image src="../../static/copy.png" style="width: 20px; height: 20px;" mode=""
						@click="handlecopy(mockList.orderId)"></image>
				</view>
			</view>
			<view class="oderItem">
				<view class="oderLeft">交易号</view>
				<view class="oderRigft">{{mockList.payId}}</view>
				<view class="arrrow" @click="handleArrow" v-if="!isShow">
					<image src="../../static/arrow-btn.png" mode=""></image>
				</view>
				<view class="arrrow-false" @click="handleArrow" v-else>
					<image src="../../static/arrow-btn.png" mode=""></image>
				</view>
			</view>
			<view class="barcode" v-if="isShow==true" style="display: flex;justify-content: center;position: relative;">
				<barcode :value="mockList.payId" format="CODE128" :height='50'  :options="barcode_option" class="my-barcode">
				</barcode>
				<view class="zd">
						
					</view>
			</view>
			<view class="oderItem">
				<view class="oderLeft">就餐方式</view>
				<view class="oderRigft">{{mockList.categoryName}}</view>
			</view>
			<view class="oderItem">
				<view class="oderLeft">预计取餐时间</view>
				<view class="oderRigft">{{mockList.expectPickUpTime}}</view>
			</view>
			<view class="oderItem">
				<view class="oderLeft">取餐码</view>
				<view class="oderRigft">{{mockList.pickupCode?mockList.pickupCode:'待出餐'}}</view>
			</view>
			<view class="oderItem">
				<view class="oderLeft">下单时间</view>
				<view class="oderRigft">{{mockList.createTime}}</view>
			</view>
			<view class="oderItem" v-if="mockList.saleTime">
				<view class="oderLeft">支付时间</view>
				<view class="oderRigft">{{mockList.saleTime}}</view>
			</view>

			<view class="oderItem" v-if="mockList.userUsePhone">
				<view class="oderLeft">用户号码</view>
				<view class="oderRigft">{{mockList.userUsePhone}}</view>
			</view>
		</view>
		<view class="bottomBtn">
			<view class="item-btn">投诉建议</view>
			<view class="item-btn">联系客服</view>
			<view class="item-agen">再来一单</view>
		</view>
	</view>
</template>

<script>
	// import mockList from '@/utils/allCity.js'
	import {
		Toast
	} from "vant"
	import VueBarcode from 'vue-barcode';
	export default {
		components: {
			'barcode': VueBarcode
		},
		data() {
			return {
				mockList: {},
				index: null,
				isShow: false,
				barcode_option: {
					displayValue: false, //是否默认显示条形码数据
					background: '#000',
				},
			};
		},
		onLoad(e) {
			try {
			uni.getStorage({
			key: 'mydata',
			success: (res) => {
				if (res.data) {
					this.mockList = JSON.parse(res.data)
				} else {
					Toast('未找到订单数据，请重新进入页面')
				}
			},
			fail: () => {
				Toast('获取订单数据失败，请重新进入页面')
			}
		})
		} catch (e) {
			console.error('数据解析失败:', e)
			Toast('订单数据异常，请联系客服')
		}

			// this.mockList = JSON.parse(decodeURIComponent(e.list))
			// console.log(this.mockList)
			this.creatQrCode()
			// this.mockList=this.$store.state.mockList
		},
		methods: {
			handleArrow() {
				this.isShow = !this.isShow
				// if(this.isShow==true){
				// }
			},
			creatQrCode() {

			},
			handlecopy(value) {
				uni.setClipboardData({
					data: value,
					success: () => {
						uni.showToast({
							title: '复制成功!',
							icon: 'none'
						})
					},
					fail: () => {
						uni.showToast({
							title: '复制失败，请重试',
							icon: 'none'
						})
					}
				})
			}
		}
	}
</script>

<style lang="less">
	page {
		background-color: #EDEDED;
	}

	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #EDEDED;
	}

	.Take {
		border-radius: 15rpx;
		overflow: hidden;
		background: url(@/static/ui/1552478.jpg);
		background-size: 100% 100%;
		width: 100%;
		height: 250rpx;
		display: flex;
		flex-direction: column;

		.code {
			font-size: 62rpx;
			font-weight: 900;
			margin: 40rpx 22rpx 0 40rpx;
		}

		.ty {
			flex: 1;
			font-size: 26rpx;
			margin: 0 40rpx;
		}
	}

	.manner {
		background-repeat: no-repeat;
		background-size: 100%;
		width: calc(100% - 40rpx);
		height: 190rpx;
		border-radius: 15rpx;
		position: relative;

		.m-right {
			display: flex;
			justify-content: end;
			position: absolute;
			top: 20rpx;
			right: 50rpx;

			image {
				width: 140rpx;
				height: 140rpx;
			}
		}
	}

	.info {
		box-sizing: border-box;
		margin-top: 20rpx;
		width: 700rpx;
		background-color: #fff;
		border-radius: 15rpx;
		padding-left: 20rpx;
		padding-top: 20rpx;
		padding-right: 20rpx;
		padding-bottom: 20rpx;

		.shopInfo {
			width: 100%;
			height: 80rpx;
			font-size: 30rpx;
			font-weight: 700;
			line-height: 80rpx;
		}

		.foodItem {
			width: 100%;

			display: flex;

			.imgs {
				width: 106rpx;
				height: 80rpx;
			}

			.foodTitle {
				box-sizing: border-box;
				flex: 1;
				padding-left: 20rpx;

				.foodName {
					width: 100%;
					line-height: 50rpx;
					font-size: 28rpx;
					font-weight: 600;
				}
			}
		}
	}

	.Oder {
		box-sizing: border-box;
		padding-top: 20rpx;
		padding-right: 20rpx;
		padding-left: 20rpx;
		width: 700rpx;
		background-color: #fff;
		border-radius: 15rpx;
		margin-top: 30rpx;
		padding-bottom: 100rpx;

		.oderTitle {
			width: 100%;
			height: 60rpx;
			line-height: 60rpx;
			font-weight: 600;
			font-size: 32rpx;
		}

		.barcode {
			position: relative;

			.zd {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				background-color: #ffffff;
				height: 65rpx;
			}
		}

		.oderItem {
			border-bottom: 1rpx dashed #EDEDED;
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 26rpx;
			display: flex;

			.arrrow {
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: auto;
				margin-left: 10rpx;

				image {
					width: 30rpx;
					height: 30rpx;
				}
			}

			.arrrow-false {
				transform: rotate(180deg);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: auto;
				margin-left: 10rpx;

				image {
					width: 30rpx;
					height: 30rpx;
				}
			}

			.oderLeft {
				flex: 1;
				text-align: left;
				color: #999;
				font-weight: 600;
			}

			.oderRigft {
				flex: 1;
				text-align: right;
			}
		}
	}

	.bottomBtn {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		height: 100rpx;
		background-color: #fff;
		justify-content: end;
		align-items: center;

		.item-agen {
			background-color: #FABB37;
			padding: 2rpx 10rpx;
			border-radius: 30rpx;
			height: 50rpx;
			line-height: 50rpx;
			margin: 0 10rpx;
		}

		.item-btn {
			padding: 2rpx 10rpx;
			border-radius: 30rpx;
			border: 1rpx solid #EDEDED;
			height: 50rpx;
			margin: 0 10rpx;
			line-height: 50rpx;
		}
	}
</style>