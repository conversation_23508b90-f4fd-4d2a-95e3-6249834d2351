<template>
	<view class="container">
		<view class="address">
			<scroll-view scroll-y="true" class="scrollView">
				<block v-if="isDate">
					<view class="addressbox" v-for="(item,index) in resList" :key='index' @click="goShopDetail(item)">
						<view class="shopimg">
							<!-- <image :src="item.mainPic" mode=""></image> -->
						</view>
						<view :class="contrLine===0?'addressnolime':'addressdetail'">
							<view class="addressdetail-top">
								<view class="" style="border-radius: 20rpx;overflow: hidden;">
									<image src="@/static/images/chooseRe/address.png" mode=""></image>
								</view>
								<view class="addressdetail-top-info">
									{{item.name}}
								</view>
							</view>
							<view class="addressdetail-center">{{item.address}}</view>
							<view class="addressdetail-bottom">
								<view class="pink" :style="item.businessStatus==1 ?'background-color: #1B5FF5;color: #fff;':''">
									<!-- 1营业 0未营业 -->
									{{item.businessStatus==1 ?'营业中':'未营业'}}
								</view>
								<view class="" style="margin:0 10rpx;">
									{{item.startTime}}am~{{item.endTime}}PM
								</view>
								<view class="address-dist" v-if="item.distanceText">
									距您{{item.distanceText}}
								</view>
							</view>
						</view>
					</view>
				</block>
				<block v-else>
					<view class="noDate">
						没有更多了...
					</view>
				</block>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		props: ['resList', 'contrLine', 'isDate','code'],
		data() {
			return {

			}
		},
		methods: {
			goShopDetail(seleStore) {
				if (seleStore.businessStatus == 1) {
					uni.navigateTo({
						url: '/pages/selectProduct/selectProduct?s='+this.code
					})
				} else {
					uni.showToast({
						title: '请重新选择餐厅！',
						icon: 'none'
					})
				}
				this.$store.commit('updateStoreCode', seleStore)
			},
		},
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #ffffff;
		border-radius: 44rpx;

		.address view:last-child {
			border: 0;
		}

		.address {
			.scrollView {
				.addressbox {
					margin-top: 20rpx;
					display: flex;
					justify-content: space-between;
					border-bottom: 1px solid rgba(153, 153, 153, 0.16);
					// padding-bottom: 36rpx;
					height: 180rpx;
					position: relative;

					.addressnolime {
						border-right: 0;
					}
					.shopimg{
						border-radius: 50%;
						overflow: hidden;
						image{
							width: 90px;
							height: 90px;
						}
					}

					.addressdetail,
					.addressnolime {
						flex: 1;
						margin-left: 20rpx;

						.addressdetail-top {
							display: flex;

							image {
								width: 22rpx;
								height: 28rpx;
							}

							.addressdetail-top-info {
								font-family: SourceHanSansSC;
								font-weight: 500;
								font-size: 28rpx;
								color: #333333;
								margin-left: 14rpx;
							}
						}

						.addressdetail-center {
							font-family: SourceHanSansSC;
							font-weight: 400;
							font-size: 24rpx;
							color: #666666;
							line-height: 36rpx;
							margin: 16rpx 0;
						}

						.addressdetail-bottom {
							display: flex;
							font-family: SourceHanSansSC;
							font-weight: 400;
							font-size: 24rpx;
							color: #999999;

							image {
								width: 98rpx;
								height: 33rpx;
							}
						}
					}

					.addressicon {
						align-items: center;
						text-align: center;
						margin: auto;
						margin-left: 10rpx;
						position: absolute;
						left: 0;
						bottom: 0;

						image {
							width: 42rpx;
							height: 42rpx;
						}

						.address-dist {
							font-family: SourceHanSansSC;
							font-weight: 400;
							font-size: 24rpx;
							color: #999999;
						}
					}
				}
			}

			.noDate {
				text-align: center;
				line-height: 100rpx;
			}
		}
	}
	.pink{
		box-sizing: border-box;
		padding: 5rpx 15rpx;
		font-size: 20rpx;
		border-radius: 10rpx;
		background-color:#eee
	}
</style>