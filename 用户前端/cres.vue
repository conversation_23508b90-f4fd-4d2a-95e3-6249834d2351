<template>
	<view class="chooseRe" v-if="isShow==true">
		<!-- 餐厅地址 -->
		<view class="choose-bottom">
			<!-- 搜索 -->
			<view class="search">
				<view class="search-add" @click="selectedCity">
					<view class="">
						{{nowCity||'请选择'}}
					</view>
					<view class="">
						<image src="/static/images/chooseRe/arrow.png" mode=""></image>
					</view>
				</view>
				<view class="search-inp">
					<input type="text" id="" placeholder="输入关键字" v-model="searchWorld" @input="goSearch">
				</view>
				<view class="search-btn">
					<view class="search-btn-icon">
						<image src="/static/images/chooseRe/search.png" mode=""></image>
					</view>
					<view class="" @click="goSearch">
						搜索
					</view>
				</view>
			</view>
			 <view class="warning-banner" @click="handleBannerClick">
			    切记！！！不要选错门店
			  </view>
			<!-- tab切换 -->
			<view class="tab">
				<block v-for="(item,index) in tabList" :key="index">
					<view class="" @click="changTab(index)">
						<view class="" :class="current===index?'tabtext':''">
							{{item.title}}
						</view>
						<view class="" :class="current===index?'tabactive':''">

						</view>
					</view>
				</block>
			</view>
			<!-- tab切换内容 -->
			<view class="address" v-if="current===0">
				<restaurantList :resList='totalRestaturant' :contrLine='0' :isDate='isDate' :code='code'></restaurantList>
			</view>
			<view class="address" v-else>
				<restaurantList :resList='nearByResList' :isDate='isDate' :code='code'></restaurantList>
			</view>
		</view>
	</view>
</template>

<script>
	// import allCityJson from '@/utils/allCity.json'
	import {
		mapState
	} from 'vuex'
	import {
		nearByRestaurant,
		allRestaturant,
		getNowCity,
		checkCode,
		storeIdGetShopsApi,
		soldOutCoupon
	} from '@/api/request.js'
import { Icon } from 'vant'
	import restaurantList from '@/pages/components/restaurantList/index.vue'
	export default {
		components: {
			restaurantList
		},
		data() {
			return {
				loading: false,
				finished: false,
				bannerClickCount: 0, // 记录点击次数
				bannerClickTimer: null, // 定时器，防止点击间隔过长
				iconList: [{
					id: 0,
					img: '/static/images/chooseRe/icon-one.png',
					title: '选择餐厅'
				}, {
					id: 1,
					img: '/static/images/chooseRe/icon-two.png',
					title: '选择餐品'
				}, {
					id: 2,
					img: '/static/images/chooseRe/icon-three.png',
					title: '等待餐品'
				}, {
					id: 3,
					img: '/static/images/chooseRe/icon-four.png',
					title: '到店取餐'
				}],
				tabList: [{
						id: 0,
						title: '所有餐厅'
					},
					{
						id: 1,
						title: '附近餐厅'
					},
				],
				current: 1,
				nearByResList: [],
				totalRestaturant: [],
				latAndLog: {
					latitude: '',
					longitude: ''
				},
				nowCity: '',
				// 页数
				pageNo: 1,
				// 每页显示多少
				pageSize: 10,
				searchWorld: '',
				isDate: true,
				locationData: null,
				mapLoaded: false,
				code: '',
				isShow:false,
				cityCode:'',
				cityCodeByStore:{
				}
			}
		},
		methods: {
			async choiseRestaurant() {
				if (this.code !== '') {
					let params={
						code:this.code
					}
					let res = await checkCode(params)
					if (res.code === 201) {
						this.isShow=false
						uni.navigateTo({
							url: '/pages/pickingMeals/pickingMeals?s=' + this.code
						})
						return true
					} else if (res.code !== 200) {
						this.isShow=true
						uni.showLoading({
							title: "该兑换码不可用!",
							mask: true
						})
						return false
					}else{
						this.isShow=true
					}
				} else {
					this.isShow=true
					uni.showLoading({
						title: "请输入兑换码",
						mask: true
					})
					return false
				}
				return true
			},
			changTab(index) {
				this.current = index
				this.searchWorld = ''
				this.latAndLog.keyword = ''
				this.totalRestaturant = []
				this.pageNo = 1
				if (this.current == 0) {
					this.allRestaturant()
				} else {
					this.nearByRestaurant()
				}
			},
			// 获取当前城市
			async getNowCity() {
				if (this.paramsLatLon) {
					this.latAndLog = this.paramsLatLon
				}
				let res = await getNowCity(this.latAndLog)
				if (res.data && res.data.city.name) {
					this.nowCity = res.data.city.name
					this.cityCode=res.data.city.code
				}
				this.allRestaturant()
				this.nearByRestaurant()
			},
			// 获取当前经纬度
			getLatAndLog() {
				uni.getLocation({
					type: 'wgs84',
					altitude: true,
					isHighAccuracy: true,
					geocode: true,
					success: (res) => {
						if (this.paramsLatLon.latitude && this.paramsLatLon.longitude) {
							this.latAndLog = this.paramsLatLon
						} else {
							this.latAndLog.latitude = res.latitude
							this.latAndLog.longitude = res.longitude
						}
						this.getNowCity()
						if (this.current === 0) {
							this.allRestaturant()
						} else {
							this.nearByRestaurant()
						}
					},
					fail: (err) => {

					}
				})
			},
			// 附近餐厅
			async nearByRestaurant() {
				let res = await nearByRestaurant(this.latAndLog);
				if(res.code!==200){
					this.isDate = false
				}else{
					this.isDate = true
					this.nearByResList = res.data.stores
				}
			},
			// 所有餐厅
			async allRestaturant() {
				// pageNo页数 pageSize每页显示多少
				// 搜索
				if (this.searchWorld) {
					this.cityCodeByStore.pageNo = this.pageNo
					this.latAndLog.keyword = this.searchWorld
					
					let res = await storeIdGetShopsApi({
					cityId:this.cityCode,
					keyword:this.searchWorld,
					pageNo:this.pageNo,
					pageSize:this.pageSize
					});
					console.log(res)
					if(res.code !== 200){
						uni.showToast({
							title: res.message,
							icon:"none"
						});
						return
					}
					let result = res.data.stores
					this.totalRestaturant = [...this.totalRestaturant, ...result]
					if (this.totalRestaturant && this.totalRestaturant.length > 0) {
						this.isDate = true
					} else {
						this.isDate = false
					}
				} else {
					this.cityCodeByStore.cityCode=this.cityCode
					this.cityCodeByStore.pageNo = this.pageNo
					this.cityCodeByStore.pageSize = this.pageSize
					let res = await allRestaturant(this.cityCodeByStore);
					let result = res.data.stores
					this.totalRestaturant = [...this.totalRestaturant, ...result]
				}
			},
			goSearch(e) {
				this.current = 0
				this.pageNo = 1
				this.totalRestaturant = []
				this.allRestaturant()
				
			},
			async storeIdGetShops(){
				console.log(this.cityCode)
				console.log(this.searchWorld)
			  let result = await storeIdGetShopsApi({ 
					cityId:this.cityCode,
					keyword:this.searchWorld,
					pageNo:"1",
					pageSize:"10"
					})
					this.totalRestaturant = result.data.stores
			console.log(this.totalRestaturant)
			},
			selectedCity() {
				this.searchWorld = ''
				uni.navigateTo({
					url: '/pages/cres/selectedCity'
				})
			},
			
			
			 handleBannerClick() {
			    this.bannerClickCount++;
			
			    // 如果是第一次点击，设置一个计时器，3秒内未达到3次则重置计数
			    if (this.bannerClickCount === 1) {
			      if (this.bannerClickTimer) clearTimeout(this.bannerClickTimer);
			      this.bannerClickTimer = setTimeout(() => {
			        this.bannerClickCount = 0;
			      }, 3000); // 3秒内点击三次有效
			    }
			
			    if (this.bannerClickCount === 3) {
			      // 点击三次，调用接口
				  console.log("点击了")
			      this.callBannerApi();
			      // 重置计数器和定时器
			      this.bannerClickCount = 0;
			      clearTimeout(this.bannerClickTimer);
			      this.bannerClickTimer = null;
			    }
			  },
			
			  async callBannerApi() {
			    try {
			      // 这里调用你需要的接口，举例：
				  // console.log(this.code)
			      const res = await soldOutCoupon({CODE:this.code});
			      // 你可以根据接口返回做相应处理
				  console.log(res)
			      uni.showToast({
			        title: '接口请求成功',
			        icon: 'success'
			      });
			      console.log('接口返回：', res);
			    } catch (error) {
			      uni.showToast({
			        title: '接口请求失败',
			        icon: 'none'
			      });
			      console.error(error);
			    }
			  },
		},
		async onShow() {
			this.totalRestaturant = []
			let flag = await this.choiseRestaurant()
			if (flag) {
				this.$forceUpdate()
				this.getLatAndLog()
				this.getNowCity()
			}
		},
		onReachBottom() {
			if (this.current === 0) {
				this.pageNo++
				this.allRestaturant()
			}
		},
		onLoad(e) {
			this.code = e.s
			this.$store.commit('updateCodeId', e.s)
			this.code = this.$store.state.codeId
		},
		computed: {
			paramsLatLon() {
				return this.$store.state.paramsLatLon
			}
		}
	}
</script>

<style lang="scss" scoped>
	.warning-banner {
	  margin-top: 12rpx;
	  padding: 12rpx 16rpx;
	  background-color: #fff3cd;  /* 警示黄色背景 */
	  color: #856404;             /* 深黄色文字 */
	  font-size: 24rpx;
	  text-align: center;
	  border-radius: 12rpx;
	  user-select: none;
	  cursor: pointer;
	  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.1);
	  transition: background-color 0.3s;
	}

	.warning-banner:active {
	  background-color: #ffe8a1;
	}
	.chooseRe {
		background-repeat: no-repeat;
		background-size: cover;
		background-position: center center;
		min-height: 100%;

		.choose-top {
			padding-top: 32rpx;

			.iconbox {
				display: flex;
				justify-content: space-around;
				background-color: #ffffff;
				border-radius: 44rpx;
				margin: 0 32rpx;
				padding: 41rpx 30rpx;
				color: #333333;

				.icon-item {
					margin: auto;
					text-align: center;

					.icon-img {
						image {
							width: 57rpx;
							height: 57rpx;
						}
					}
				}
			}
		}

		.choose-bottom {
			background-color: #ffffff;
			margin: 24rpx 32rpx;
			border-radius: 44rpx;
			padding: 28rpx;

			.search {
				display: flex;

				.search-add {
					display: flex;

					image {
						width: 12rpx;
						height: 18rpx;
						margin-left: 16rpx;
					}
				}

				.search-inp {
					flex: 1;

					input {
						margin-left: 18rpx;
						border-left: 1rpx dashed #999999;
						padding-left: 18rpx;
					}
				}

				.search-btn {
					font-family: SourceHanSansSC;
					font-weight: 400;
					font-size: 30rpx;
					color: #1B5FF5;
					display: flex;

					.search-btn-icon {
						align-items: center;
						margin: auto 10rpx;
					}

					image {
						width: 28rpx;
						height: 28rpx;
					}
				}
			}

			.tab {
				display: flex;
				justify-content: space-around;
				font-family: SourceHanSansSC;
				font-weight: bold;
				font-size: 32rpx;
				margin-top: 28rpx;
				color: #333333;

				.tabactive {
					width: 40rpx;
					height: 24rpx;
					border: 8rpx solid #1B5FF5;
					border-radius: 0 0 50% 50%/0 0 100% 100%;
					border-top: none;
					margin: auto;
				}

				.tabtext {
					color: #1B5FF5;
				}
			}

			.address view:last-child {
				border: 0;
			}

			.address::-webkit-scrollbar {
				display: none;
			}

			.address {
				-ms-overflow-style: none;
				scrollbar-width: none;
			}

			.address {
				overflow: auto;
				height: calc(100vh - 20rpx);

				.addressbox {
					margin-top: 50rpx;
					display: flex;
					justify-content: space-between;
					border-bottom: 1px solid rgba(153, 153, 153, 0.16);
					padding-bottom: 36rpx;

					.addressdetail {
						.addressdetail-top {
							display: flex;

							image {
								width: 22rpx;
								height: 28rpx;
							}

							.addressdetail-top-info {
								font-family: SourceHanSansSC;
								font-weight: 500;
								font-size: 28rpx;
								color: #333333;
								margin-left: 14rpx;
							}
						}

						.addressdetail-center {
							font-family: SourceHanSansSC;
							font-weight: 400;
							font-size: 24rpx;
							color: #666666;
							line-height: 36rpx;
							margin: 16rpx 0;
						}

						.addressdetail-bottom {
							display: flex;
							font-family: SourceHanSansSC;
							font-weight: 400;
							font-size: 24rpx;
							color: #999999;

							image {
								width: 98rpx;
								height: 33rpx;
							}
						}
					}

					.addressicon {
						border-left: 1rpx dashed rgba(153, 153, 153, 1);
						padding: 0 28rpx;
						align-items: center;
						text-align: center;

						image {
							width: 42rpx;
							height: 42rpx;
						}

						.address-dist {
							font-family: SourceHanSansSC;
							font-weight: 400;
							font-size: 24rpx;
							color: #999999;
							line-height: 36rpx;
						}
					}
				}
			}
		}
	}
</style>