#!/usr/bin/env python3
"""
简单的启动测试
"""
import os
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

print("🚀 开始启动测试...")

# 检查必需的环境变量
required_vars = {
    'JWT_SECRET': os.getenv('JWT_SECRET'),
    'DB_PASSWORD': os.getenv('DB_PASSWORD'),
    'MCD_API_DEVICE_ID': os.getenv('MCD_API_DEVICE_ID')
}

print("\n📋 环境变量检查:")
for var, value in required_vars.items():
    status = "✅" if value else "❌"
    display_value = value[:20] + "..." if value and len(value) > 20 else value
    print(f"{status} {var}: {display_value}")

# 尝试启动FastAPI
try:
    print("\n🔧 导入FastAPI...")
    from fastapi import FastAPI
    
    print("✅ FastAPI导入成功")
    
    # 创建简单的应用
    app = FastAPI(title="MCD API Server", version="1.0.0")
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "message": "MCD API Server is running"}
    
    @app.get("/")
    async def root():
        return {"message": "Welcome to MCD API Server"}
    
    print("✅ FastAPI应用创建成功")
    
    # 测试启动
    print("\n🚀 启动服务器...")
    import uvicorn
    
    # 使用简单配置启动
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
    
except Exception as e:
    print(f"\n❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
