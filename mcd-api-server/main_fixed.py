#!/usr/bin/env python3
"""
MCD API Server - 修复版本
麦当劳API代理服务器
"""

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
from datetime import datetime

# 导入简化配置
from config.simple_config import config

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="MCD API Server",
    description="麦当劳API代理服务器",
    version="1.0.0",
    debug=config.debug
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.cors_origins_list,
    allow_credentials=True,
    allow_methods=config.cors_methods_list,
    allow_headers=config.cors_headers_list,
)

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "MCD API Server is running",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "environment": config.environment
    }

# 根端点
@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "Welcome to MCD API Server",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

# 配置信息端点（仅开发环境）
@app.get("/config")
async def get_config():
    """获取配置信息（仅开发环境）"""
    if not config.is_development():
        raise HTTPException(status_code=404, detail="Not found")
    
    return {
        "environment": config.environment,
        "host": config.host,
        "port": config.port,
        "debug": config.debug,
        "database": {
            "host": config.db_host,
            "port": config.db_port,
            "database": config.db_database
        },
        "redis": {
            "host": config.redis_host,
            "port": config.redis_port,
            "db": config.redis_db
        },
        "mcd_api": {
            "base_url": config.mcd_api_base_url,
            "timeout": config.mcd_api_timeout,
            "max_retries": config.mcd_api_max_retries
        }
    }

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc) if config.is_development() else "An error occurred"
        }
    )

# 启动事件
@app.on_event("startup")
async def startup_event():
    """启动事件"""
    logger.info("🚀 MCD API Server starting up...")
    logger.info(f"Environment: {config.environment}")
    logger.info(f"Debug mode: {config.debug}")
    logger.info(f"Database: {config.db_host}:{config.db_port}/{config.db_database}")
    logger.info(f"Redis: {config.redis_host}:{config.redis_port}/{config.redis_db}")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """关闭事件"""
    logger.info("🛑 MCD API Server shutting down...")

if __name__ == "__main__":
    logger.info("🚀 Starting MCD API Server...")
    
    # 启动服务器
    uvicorn.run(
        "main_fixed:app",
        host=config.host,
        port=config.port,
        log_level=config.log_level.lower(),
        reload=config.is_development(),
        workers=1 if config.is_development() else 4
    )
