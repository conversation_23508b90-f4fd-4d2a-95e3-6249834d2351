"""
简化的配置管理
兼容Pydantic v2
"""
import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class AppConfig(BaseSettings):
    """应用配置"""
    model_config = SettingsConfigDict(
        env_file='.env',
        env_file_encoding='utf-8',
        extra='ignore'  # 忽略额外字段
    )
    
    # 环境配置
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", env="FASTAPI_HOST")
    port: int = Field(default=8000, env="FASTAPI_PORT")
    debug: bool = Field(default=False, env="FASTAPI_DEBUG")
    log_level: str = Field(default="INFO", env="FASTAPI_LOG_LEVEL")
    
    # 数据库配置
    db_host: str = Field(default="127.0.0.1", env="DB_HOST")
    db_port: int = Field(default=3306, env="DB_PORT")
    db_username: str = Field(default="root", env="DB_USERNAME")
    db_password: str = Field(env="DB_PASSWORD")
    db_database: str = Field(default="mcd_api", env="DB_DATABASE")
    
    # Redis配置
    redis_host: str = Field(default="127.0.0.1", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_db: int = Field(default=0, env="REDIS_DB")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # JWT配置
    jwt_secret: str = Field(env="JWT_SECRET")
    jwt_expires_in: str = Field(default="24h", env="JWT_EXPIRES_IN")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    
    # MCD API配置
    mcd_api_base_url: str = Field(default="https://api.mcd.cn", env="MCD_API_BASE_URL")
    mcd_api_device_id: str = Field(env="MCD_API_DEVICE_ID")
    mcd_api_tid: str = Field(default="00003TuN", env="MCD_API_TID")
    mcd_api_timeout: int = Field(default=30, env="MCD_API_TIMEOUT")
    mcd_api_max_retries: int = Field(default=3, env="MCD_API_MAX_RETRIES")
    
    # CORS配置
    cors_origins: str = Field(default="*", env="CORS_ORIGINS")
    cors_methods: str = Field(default="GET,POST,PUT,DELETE,OPTIONS", env="CORS_METHODS")
    cors_headers: str = Field(default="*", env="CORS_HEADERS")
    
    # 安全配置
    security_session_secret: str = Field(env="SECURITY_SESSION_SECRET")
    security_encryption_key: str = Field(env="SECURITY_ENCRYPTION_KEY")
    
    @property
    def cors_origins_list(self):
        """获取CORS origins列表"""
        if self.cors_origins == "*":
            return ["*"]
        return [origin.strip() for origin in self.cors_origins.split(',')]
    
    @property
    def cors_methods_list(self):
        """获取CORS methods列表"""
        return [method.strip() for method in self.cors_methods.split(',')]
    
    @property
    def cors_headers_list(self):
        """获取CORS headers列表"""
        if self.cors_headers == "*":
            return ["*"]
        return [header.strip() for header in self.cors_headers.split(',')]
    
    @property
    def database_url(self):
        """获取数据库连接URL"""
        return f"mysql+aiomysql://{self.db_username}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_database}"
    
    def is_development(self):
        """是否为开发环境"""
        return self.environment.lower() == "development"
    
    def is_production(self):
        """是否为生产环境"""
        return self.environment.lower() == "production"


# 创建全局配置实例
try:
    config = AppConfig()
    print("✅ 配置加载成功")
except Exception as e:
    print(f"❌ 配置加载失败: {e}")
    raise
