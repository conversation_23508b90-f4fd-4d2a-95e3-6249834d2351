#!/usr/bin/env python3
"""
测试配置加载
"""
import os
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

print("环境变量测试:")
print(f"JWT_SECRET: {os.getenv('JWT_SECRET')}")
print(f"DB_PASSWORD: {os.getenv('DB_PASSWORD')}")
print(f"SERVER_PORT: {os.getenv('SERVER_PORT')}")

# 测试配置类
try:
    from config.env_config import JWTConfig, DatabaseConfig
    
    print("\n配置类测试:")
    jwt_config = JWTConfig()
    print(f"JWT Secret: {jwt_config.secret[:10]}...")
    print(f"JWT Algorithm: {jwt_config.algorithm}")
    
    db_config = DatabaseConfig()
    print(f"DB Host: {db_config.host}")
    print(f"DB Port: {db_config.port}")
    
    print("\n✅ 配置加载成功!")
    
except Exception as e:
    print(f"\n❌ 配置加载失败: {e}")
