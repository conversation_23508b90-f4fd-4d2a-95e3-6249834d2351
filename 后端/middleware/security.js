/**
 * 安全中间件
 * 提供各种安全防护功能
 */

const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const validator = require('validator');
const config = require('../config/unified_config');
const ErrorHandler = require('./error_handler');

class SecurityMiddleware {
    /**
     * 基础安全头设置
     */
    static basicSecurity() {
        return helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                    connectSrc: ["'self'"],
                    fontSrc: ["'self'"],
                    objectSrc: ["'none'"],
                    mediaSrc: ["'self'"],
                    frameSrc: ["'none'"],
                },
            },
            crossOriginEmbedderPolicy: false, // 根据需要调整
        });
    }

    /**
     * 限流中间件
     */
    static rateLimiter() {
        return rateLimit({
            windowMs: config.get('security.rateLimitWindow'),
            max: config.get('security.rateLimitMax'),
            message: {
                success: false,
                code: 429,
                message: '请求过于频繁，请稍后再试',
                errorCode: 'RATE_LIMIT_EXCEEDED'
            },
            standardHeaders: true,
            legacyHeaders: false,
            handler: ErrorHandler.rateLimitHandler
        });
    }

    /**
     * API限流（更严格）
     */
    static apiRateLimiter() {
        return rateLimit({
            windowMs: 60000, // 1分钟
            max: 30, // 每分钟最多30次请求
            message: {
                success: false,
                code: 429,
                message: 'API调用过于频繁，请稍后再试',
                errorCode: 'API_RATE_LIMIT_EXCEEDED'
            },
            standardHeaders: true,
            legacyHeaders: false,
            skip: (req) => {
                // 跳过健康检查和静态资源
                return req.path === '/health' || req.path.startsWith('/static');
            }
        });
    }

    /**
     * 输入验证中间件
     */
    static validateInput() {
        return (req, res, next) => {
            // 验证常见的危险输入
            const dangerousPatterns = [
                /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, // XSS
                /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi, // SQL注入
                /(javascript:|vbscript:|onload=|onerror=)/gi, // 脚本注入
                /(\.\.|\/\.\.|\.\.\/)/g, // 路径遍历
            ];

            const checkValue = (value) => {
                if (typeof value === 'string') {
                    for (const pattern of dangerousPatterns) {
                        if (pattern.test(value)) {
                            return false;
                        }
                    }
                }
                return true;
            };

            const validateObject = (obj) => {
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        const value = obj[key];
                        if (typeof value === 'object' && value !== null) {
                            if (!validateObject(value)) return false;
                        } else if (!checkValue(value)) {
                            return false;
                        }
                    }
                }
                return true;
            };

            // 检查请求体
            if (req.body && !validateObject(req.body)) {
                return next(new ErrorHandler.ValidationError('请求包含非法字符'));
            }

            // 检查查询参数
            if (req.query && !validateObject(req.query)) {
                return next(new ErrorHandler.ValidationError('查询参数包含非法字符'));
            }

            next();
        };
    }

    /**
     * SQL注入防护
     */
    static sqlInjectionProtection() {
        return (req, res, next) => {
            const sqlPatterns = [
                /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
                /((\%27)|(\')|((\%3D)|(=))[^\n]*((\%27)|(\')|((\%2D)|(-)))/gi,
                /((\%27)|(\'))union/gi,
                /exec(\s|\+)+(s|x)p\w+/gi,
            ];

            const checkSqlInjection = (value) => {
                if (typeof value === 'string') {
                    return sqlPatterns.some(pattern => pattern.test(value));
                }
                return false;
            };

            const validateForSql = (obj) => {
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        const value = obj[key];
                        if (typeof value === 'object' && value !== null) {
                            if (validateForSql(value)) return true;
                        } else if (checkSqlInjection(value)) {
                            return true;
                        }
                    }
                }
                return false;
            };

            if ((req.body && validateForSql(req.body)) || 
                (req.query && validateForSql(req.query))) {
                return next(new ErrorHandler.ValidationError('检测到SQL注入尝试'));
            }

            next();
        };
    }

    /**
     * XSS防护
     */
    static xssProtection() {
        return (req, res, next) => {
            const xssPatterns = [
                /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
                /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
                /javascript:/gi,
                /vbscript:/gi,
                /onload\s*=/gi,
                /onerror\s*=/gi,
                /onclick\s*=/gi,
                /onmouseover\s*=/gi,
            ];

            const checkXss = (value) => {
                if (typeof value === 'string') {
                    return xssPatterns.some(pattern => pattern.test(value));
                }
                return false;
            };

            const validateForXss = (obj) => {
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        const value = obj[key];
                        if (typeof value === 'object' && value !== null) {
                            if (validateForXss(value)) return true;
                        } else if (checkXss(value)) {
                            return true;
                        }
                    }
                }
                return false;
            };

            if ((req.body && validateForXss(req.body)) || 
                (req.query && validateForXss(req.query))) {
                return next(new ErrorHandler.ValidationError('检测到XSS攻击尝试'));
            }

            next();
        };
    }

    /**
     * 参数验证
     */
    static validateParams(rules) {
        return (req, res, next) => {
            const errors = [];

            for (const field in rules) {
                const rule = rules[field];
                const value = req.body[field] || req.query[field] || req.params[field];

                // 必填验证
                if (rule.required && (value === undefined || value === null || value === '')) {
                    errors.push(`${field} 是必填字段`);
                    continue;
                }

                if (value !== undefined && value !== null && value !== '') {
                    // 类型验证
                    if (rule.type === 'email' && !validator.isEmail(value)) {
                        errors.push(`${field} 必须是有效的邮箱地址`);
                    }
                    
                    if (rule.type === 'phone' && !validator.isMobilePhone(value, 'zh-CN')) {
                        errors.push(`${field} 必须是有效的手机号码`);
                    }
                    
                    if (rule.type === 'number' && !validator.isNumeric(value.toString())) {
                        errors.push(`${field} 必须是数字`);
                    }
                    
                    if (rule.type === 'url' && !validator.isURL(value)) {
                        errors.push(`${field} 必须是有效的URL`);
                    }

                    // 长度验证
                    if (rule.minLength && value.length < rule.minLength) {
                        errors.push(`${field} 长度不能少于 ${rule.minLength} 个字符`);
                    }
                    
                    if (rule.maxLength && value.length > rule.maxLength) {
                        errors.push(`${field} 长度不能超过 ${rule.maxLength} 个字符`);
                    }

                    // 正则验证
                    if (rule.pattern && !rule.pattern.test(value)) {
                        errors.push(`${field} 格式不正确`);
                    }

                    // 自定义验证
                    if (rule.validator && !rule.validator(value)) {
                        errors.push(rule.message || `${field} 验证失败`);
                    }
                }
            }

            if (errors.length > 0) {
                return next(new ErrorHandler.ValidationError(errors.join('; ')));
            }

            next();
        };
    }

    /**
     * IP白名单验证
     */
    static ipWhitelist(allowedIps = []) {
        return (req, res, next) => {
            if (allowedIps.length === 0) {
                return next(); // 没有配置白名单，跳过验证
            }

            const clientIp = req.ip || req.connection.remoteAddress;
            
            if (!allowedIps.includes(clientIp)) {
                return next(new ErrorHandler.PermissionError('IP地址不在白名单中'));
            }

            next();
        };
    }

    /**
     * 请求大小限制
     */
    static requestSizeLimit(maxSize = '10mb') {
        return (req, res, next) => {
            const contentLength = req.get('content-length');
            
            if (contentLength) {
                const sizeInMB = parseInt(contentLength) / (1024 * 1024);
                const maxSizeInMB = parseInt(maxSize);
                
                if (sizeInMB > maxSizeInMB) {
                    return next(new ErrorHandler.ValidationError(`请求体大小不能超过 ${maxSize}`));
                }
            }

            next();
        };
    }
}

module.exports = SecurityMiddleware;
