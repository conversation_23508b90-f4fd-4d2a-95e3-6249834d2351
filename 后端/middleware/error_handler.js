/**
 * 统一错误处理中间件
 * 提供标准化的错误响应和日志记录
 */

const { errLog } = require('../utils/err');
const config = require('../config/unified_config');

class ErrorHandler {
    /**
     * 全局错误处理中间件
     */
    static globalErrorHandler(err, req, res, next) {
        // 设置默认错误信息
        let statusCode = err.statusCode || err.status || 500;
        let message = err.message || '服务器内部错误';
        let errorCode = err.code || 'INTERNAL_ERROR';

        // 根据错误类型设置不同的状态码和消息
        if (err.name === 'ValidationError') {
            statusCode = 400;
            message = '请求参数验证失败';
            errorCode = 'VALIDATION_ERROR';
        } else if (err.name === 'UnauthorizedError' || err.name === 'JsonWebTokenError') {
            statusCode = 401;
            message = '认证失败';
            errorCode = 'AUTH_ERROR';
        } else if (err.name === 'ForbiddenError') {
            statusCode = 403;
            message = '权限不足';
            errorCode = 'PERMISSION_ERROR';
        } else if (err.name === 'NotFoundError') {
            statusCode = 404;
            message = '资源不存在';
            errorCode = 'NOT_FOUND';
        } else if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
            statusCode = 503;
            message = '外部服务不可用';
            errorCode = 'SERVICE_UNAVAILABLE';
        } else if (err.code === 'ECONNABORTED') {
            statusCode = 408;
            message = '请求超时';
            errorCode = 'REQUEST_TIMEOUT';
        }

        // 记录错误日志
        errLog({
            err,
            code: statusCode,
            msg: message,
            funName: 'GlobalErrorHandler',
            req: {
                method: req.method,
                url: req.url,
                headers: req.headers,
                body: req.body,
                ip: req.ip,
                userAgent: req.get('User-Agent')
            }
        });

        // 构建错误响应
        const errorResponse = {
            success: false,
            code: statusCode,
            message,
            errorCode,
            timestamp: new Date().toISOString(),
            path: req.path
        };

        // 开发环境下包含详细错误信息
        if (config.isDevelopment()) {
            errorResponse.details = {
                stack: err.stack,
                name: err.name,
                originalError: err
            };
        }

        // 发送错误响应
        res.status(statusCode).json(errorResponse);
    }

    /**
     * 404错误处理
     */
    static notFoundHandler(req, res, next) {
        const error = new Error(`路由不存在: ${req.method} ${req.path}`);
        error.statusCode = 404;
        error.code = 'NOT_FOUND';
        next(error);
    }

    /**
     * 异步错误包装器
     */
    static asyncWrapper(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch(next);
        };
    }

    /**
     * 业务错误类
     */
    static BusinessError = class extends Error {
        constructor(message, code = 400, errorCode = 'BUSINESS_ERROR') {
            super(message);
            this.name = 'BusinessError';
            this.statusCode = code;
            this.errorCode = errorCode;
        }
    };

    /**
     * 验证错误类
     */
    static ValidationError = class extends Error {
        constructor(message, field = null) {
            super(message);
            this.name = 'ValidationError';
            this.statusCode = 400;
            this.errorCode = 'VALIDATION_ERROR';
            this.field = field;
        }
    };

    /**
     * 认证错误类
     */
    static AuthError = class extends Error {
        constructor(message = '认证失败') {
            super(message);
            this.name = 'AuthError';
            this.statusCode = 401;
            this.errorCode = 'AUTH_ERROR';
        }
    };

    /**
     * 权限错误类
     */
    static PermissionError = class extends Error {
        constructor(message = '权限不足') {
            super(message);
            this.name = 'PermissionError';
            this.statusCode = 403;
            this.errorCode = 'PERMISSION_ERROR';
        }
    };

    /**
     * 资源不存在错误类
     */
    static NotFoundError = class extends Error {
        constructor(message = '资源不存在') {
            super(message);
            this.name = 'NotFoundError';
            this.statusCode = 404;
            this.errorCode = 'NOT_FOUND';
        }
    };

    /**
     * 请求验证中间件
     */
    static validateRequest(schema) {
        return (req, res, next) => {
            const { error } = schema.validate(req.body);
            if (error) {
                const validationError = new ErrorHandler.ValidationError(
                    error.details[0].message,
                    error.details[0].path[0]
                );
                return next(validationError);
            }
            next();
        };
    }

    /**
     * 限流错误处理
     */
    static rateLimitHandler(req, res, next) {
        const error = new Error('请求过于频繁，请稍后再试');
        error.statusCode = 429;
        error.errorCode = 'RATE_LIMIT_EXCEEDED';
        next(error);
    }

    /**
     * 数据库错误处理
     */
    static handleDatabaseError(err) {
        if (err.code === 'ER_DUP_ENTRY') {
            return new ErrorHandler.BusinessError('数据已存在', 409, 'DUPLICATE_ENTRY');
        } else if (err.code === 'ER_NO_REFERENCED_ROW_2') {
            return new ErrorHandler.BusinessError('关联数据不存在', 400, 'FOREIGN_KEY_ERROR');
        } else if (err.code === 'ER_ROW_IS_REFERENCED_2') {
            return new ErrorHandler.BusinessError('数据被其他记录引用，无法删除', 400, 'REFERENCED_ERROR');
        } else if (err.code === 'ECONNREFUSED') {
            return new ErrorHandler.BusinessError('数据库连接失败', 503, 'DATABASE_UNAVAILABLE');
        }
        
        return new ErrorHandler.BusinessError('数据库操作失败', 500, 'DATABASE_ERROR');
    }

    /**
     * API错误处理
     */
    static handleApiError(err) {
        if (err.response) {
            // 外部API返回错误
            const status = err.response.status;
            const message = err.response.data?.message || '外部API调用失败';
            return new ErrorHandler.BusinessError(message, status, 'EXTERNAL_API_ERROR');
        } else if (err.code === 'ECONNABORTED') {
            return new ErrorHandler.BusinessError('API调用超时', 408, 'API_TIMEOUT');
        } else if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
            return new ErrorHandler.BusinessError('外部服务不可用', 503, 'SERVICE_UNAVAILABLE');
        }
        
        return new ErrorHandler.BusinessError('API调用失败', 500, 'API_ERROR');
    }
}

module.exports = ErrorHandler;
