const mysql = require('mysql2');
const config = require('./config/unified_config');

// 数据库连接配置
const dbConfig = {
    host: config.get('database.host'),
    port: config.get('database.port'),
    user: config.get('database.user'),
    password: config.get('database.password'),
    database: config.get('database.database'),
    connectionLimit: config.get('database.connectionLimit'),
    queueLimit: 0,
    acquireTimeout: config.get('database.acquireTimeout'),
    timeout: config.get('database.timeout'),
    charset: config.get('database.charset'),
    timezone: config.get('database.timezone'),
    supportBigNumbers: true,
    bigNumberStrings: true,
    reconnect: true,

    // 新增优化配置
    idleTimeout: 300000, // 5分钟空闲超时
    enableKeepAlive: true,
    keepAliveInitialDelay: 0,

    // 连接池事件监听
    multipleStatements: false, // 安全考虑，禁用多语句
    ssl: false, // 根据需要启用SSL

    // 性能优化
    dateStrings: false,
    debug: config.isDevelopment(),
    trace: config.isDevelopment()
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 连接池事件监听
pool.on('connection', (connection) => {
    console.log(`✅ 新数据库连接建立: ${connection.threadId}`);
});

pool.on('error', (err) => {
    console.error('❌ 数据库连接池错误:', err);
    if (err.code === 'PROTOCOL_CONNECTION_LOST') {
        console.log('🔄 数据库连接丢失，尝试重连...');
    }
});

pool.on('release', (connection) => {
    console.log(`🔄 数据库连接释放: ${connection.threadId}`);
});

// 测试连接
pool.getConnection((err, connection) => {
    if (err) {
        console.error('❌ 数据库连接失败:', err);
        process.exit(1);
    }
    console.log('✅ 数据库连接成功');

    // 测试查询
    connection.query('SELECT 1 as test', (err, results) => {
        if (err) {
            console.error('❌ 数据库查询测试失败:', err);
        } else {
            console.log('✅ 数据库查询测试成功');
        }
        connection.release();
    });
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('🔄 正在关闭数据库连接池...');
    pool.end(() => {
        console.log('✅ 数据库连接池已关闭');
        process.exit(0);
    });
});

// 导出连接池和Promise版本
module.exports = {
    pool,
    promisePool: pool.promise() // 提供Promise版本的连接池
};
