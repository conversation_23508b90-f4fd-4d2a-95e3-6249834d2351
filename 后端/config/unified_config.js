/**
 * 统一配置管理系统
 * 整合所有配置项，支持环境变量覆盖
 */

require('dotenv').config();

class ConfigManager {
    constructor() {
        this.config = this.loadConfig();
        this.validateConfig();
    }

    loadConfig() {
        return {
            // 服务器配置
            server: {
                port: parseInt(process.env.BACKEND_PORT) || 3000,
                host: process.env.BACKEND_HOST || '0.0.0.0',
                nodeEnv: process.env.NODE_ENV || 'production',
                debug: process.env.DEBUG === 'true',
                apiDocsUrl: process.env.API_DOCS_URL || 'http://localhost:3000'
            },

            // 数据库配置
            database: {
                host: process.env.DB_HOST || 'mysql',
                port: parseInt(process.env.DB_PORT) || 3306,
                user: process.env.DB_USER || 'mdl_server',
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME || 'mdl_server',
                connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 20,
                acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
                timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 60000,
                charset: 'utf8mb4',
                timezone: '+08:00'
            },

            // Redis配置
            redis: {
                host: process.env.REDIS_HOST || 'redis',
                port: parseInt(process.env.REDIS_PORT) || 6379,
                password: process.env.REDIS_PASSWORD || '',
                db: parseInt(process.env.REDIS_DB) || 0,
                connectTimeout: 10000,
                commandTimeout: 5000,
                retryDelayOnFailover: 100,
                maxRetriesPerRequest: 3
            },

            // MCD API配置
            mcdApi: {
                // 使用本地代理还是外部API
                useLocalProxy: process.env.MCD_USE_LOCAL_PROXY === 'true',
                localProxyUrl: process.env.MCD_LOCAL_PROXY_URL || 'http://mcd-api-server:8000',
                externalApiUrl: process.env.MCD_EXTERNAL_API_URL || 'http://server.pqkap.com',
                officialApiUrl: process.env.MCD_OFFICIAL_API_URL || 'https://api.mcd.cn',
                
                // API配置
                timeout: parseInt(process.env.MCD_API_TIMEOUT) || 30000,
                maxRetries: parseInt(process.env.MCD_API_MAX_RETRIES) || 3,
                retryDelay: parseInt(process.env.MCD_API_RETRY_DELAY) || 1000,
                
                // 认证配置
                appVersion: process.env.MCD_API_APP_VERSION || '********',
                deviceId: process.env.MCD_API_DEVICE_ID,
                tid: process.env.MCD_API_TID || '00003TuN'
            },

            // CORS配置
            cors: {
                origins: this.parseArray(process.env.CORS_ORIGINS) || [
                    'http://localhost:3000',
                    'http://localhost:8080',
                    'http://localhost:8081',
                    'http://127.0.0.1:3000',
                    'http://127.0.0.1:8080',
                    'http://127.0.0.1:8081'
                ],
                credentials: process.env.CORS_CREDENTIALS !== 'false',
                methods: this.parseArray(process.env.CORS_METHODS) || ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
                allowedHeaders: this.parseArray(process.env.CORS_ALLOWED_HEADERS) || [
                    'Content-Type', 'Authorization', 'X-Requested-With', 'token'
                ],
                exposedHeaders: this.parseArray(process.env.CORS_EXPOSED_HEADERS) || ['X-Total-Count']
            },

            // JWT配置
            jwt: {
                secret: process.env.JWT_SECRET,
                expiresIn: process.env.JWT_EXPIRES_IN || '86400s',
                algorithm: process.env.JWT_ALGORITHM || 'HS256'
            },

            // 业务配置
            business: {
                domainPrefix: process.env.BUSINESS_DOMAIN_PREFIX || 'localhost:8081',
                domainUrl: process.env.BUSINESS_DOMAIN_URL || 'https://xiadan.pqkap.com/?s=',
                signKey: process.env.BUSINESS_SIGN_KEY,
                
                // 算法服务配置
                algorithmGetUrl: process.env.ALGORITHM_GET_URL || 'http://127.0.0.1:8081/sign/get',
                algorithmPostUrl: process.env.ALGORITHM_POST_URL || 'http://127.0.0.1:8081/sign/post'
            },

            // 定时任务配置
            schedule: {
                resetMemberRights: process.env.SCHEDULE_RESET_MEMBER_RIGHTS || '2 25 0 * * *',
                updateCoupons: process.env.SCHEDULE_UPDATE_COUPONS || '1 26 0 * * *',
                cleanupOrders: process.env.SCHEDULE_CLEANUP_ORDERS || '* * * * * *',
                deleteExpiredCoupons: process.env.SCHEDULE_DELETE_EXPIRED_COUPONS || '1 27 0 * * *'
            },

            // 应用配置
            app: {
                logLevel: process.env.LOG_LEVEL || 'INFO',
                maxUploadSize: parseInt(process.env.MAX_UPLOAD_SIZE) || 10,
                sessionTimeout: parseInt(process.env.SESSION_TIMEOUT) || 30,
                staticPath: process.env.STATIC_PATH || './public'
            },

            // 安全配置
            security: {
                rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 900000, // 15分钟
                rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 100,
                bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
                sessionSecret: process.env.SESSION_SECRET
            }
        };
    }

    parseArray(str) {
        if (!str) return null;
        return str.split(',').map(item => item.trim()).filter(item => item);
    }

    validateConfig() {
        const required = [
            'database.password',
            'jwt.secret',
            'business.signKey',
            'security.sessionSecret'
        ];

        const missing = [];
        
        for (const path of required) {
            if (!this.getNestedValue(this.config, path)) {
                missing.push(path);
            }
        }

        if (missing.length > 0) {
            console.error('❌ 缺少必需的配置项:', missing);
            console.error('请检查环境变量配置');
            process.exit(1);
        }

        // 验证MCD API配置
        if (this.config.mcdApi.useLocalProxy && !this.config.mcdApi.localProxyUrl) {
            console.error('❌ 启用本地代理但未配置代理URL');
            process.exit(1);
        }

        console.log('✅ 配置验证通过');
    }

    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    get(path) {
        return this.getNestedValue(this.config, path) || this.config;
    }

    // 获取API基础URL
    getApiBaseUrl() {
        if (this.config.mcdApi.useLocalProxy) {
            return this.config.mcdApi.localProxyUrl;
        }
        return this.config.mcdApi.externalApiUrl;
    }

    // 获取完整的API URL
    buildApiUrl(endpoint) {
        const baseUrl = this.getApiBaseUrl();
        return `${baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`;
    }

    // 开发环境检查
    isDevelopment() {
        return this.config.server.nodeEnv === 'development';
    }

    // 生产环境检查
    isProduction() {
        return this.config.server.nodeEnv === 'production';
    }
}

// 创建全局配置实例
const configManager = new ConfigManager();

module.exports = configManager;
