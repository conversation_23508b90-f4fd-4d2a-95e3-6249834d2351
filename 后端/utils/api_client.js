/**
 * 统一API客户端
 * 提供统一的API调用接口，支持重试、超时、错误处理
 */

const axios = require('axios');
const config = require('../config/unified_config');
const { errLog } = require('./err');

class ApiClient {
    constructor() {
        this.baseURL = config.getApiBaseUrl();
        this.timeout = config.get('mcdApi.timeout');
        this.maxRetries = config.get('mcdApi.maxRetries');
        this.retryDelay = config.get('mcdApi.retryDelay');
        
        // 创建axios实例
        this.client = axios.create({
            baseURL: this.baseURL,
            timeout: this.timeout,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'MCD-Backend/1.0.0'
            }
        });

        // 设置请求拦截器
        this.setupInterceptors();
    }

    setupInterceptors() {
        // 请求拦截器
        this.client.interceptors.request.use(
            (config) => {
                // 添加请求ID用于追踪
                config.metadata = { startTime: Date.now() };
                console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`);
                return config;
            },
            (error) => {
                console.error('❌ 请求拦截器错误:', error);
                return Promise.reject(error);
            }
        );

        // 响应拦截器
        this.client.interceptors.response.use(
            (response) => {
                const duration = Date.now() - response.config.metadata.startTime;
                console.log(`✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);
                return response;
            },
            async (error) => {
                const duration = error.config?.metadata ? Date.now() - error.config.metadata.startTime : 0;
                console.error(`❌ API错误: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, error.message);
                
                // 记录错误日志
                errLog({
                    err: error,
                    code: error.response?.status || 500,
                    msg: `API调用失败: ${error.config?.url}`,
                    funName: 'ApiClient'
                });

                return Promise.reject(error);
            }
        );
    }

    /**
     * 带重试的请求方法
     */
    async requestWithRetry(config, retryCount = 0) {
        try {
            const response = await this.client.request(config);
            return response.data;
        } catch (error) {
            // 判断是否需要重试
            if (this.shouldRetry(error, retryCount)) {
                console.log(`🔄 重试请求 (${retryCount + 1}/${this.maxRetries}): ${config.url}`);
                await this.delay(this.retryDelay * Math.pow(2, retryCount)); // 指数退避
                return this.requestWithRetry(config, retryCount + 1);
            }
            throw error;
        }
    }

    /**
     * 判断是否应该重试
     */
    shouldRetry(error, retryCount) {
        if (retryCount >= this.maxRetries) return false;
        
        // 网络错误或5xx错误才重试
        if (error.code === 'ECONNABORTED' || error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            return true;
        }
        
        if (error.response && error.response.status >= 500) {
            return true;
        }
        
        return false;
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * GET请求
     */
    async get(url, params = {}, options = {}) {
        return this.requestWithRetry({
            method: 'GET',
            url,
            params,
            ...options
        });
    }

    /**
     * POST请求
     */
    async post(url, data = {}, options = {}) {
        return this.requestWithRetry({
            method: 'POST',
            url,
            data,
            ...options
        });
    }

    /**
     * PUT请求
     */
    async put(url, data = {}, options = {}) {
        return this.requestWithRetry({
            method: 'PUT',
            url,
            data,
            ...options
        });
    }

    /**
     * DELETE请求
     */
    async delete(url, options = {}) {
        return this.requestWithRetry({
            method: 'DELETE',
            url,
            ...options
        });
    }

    /**
     * 麦当劳API专用方法
     */
    
    // 获取城市列表
    async getCities() {
        return this.get('/store/cities/group');
    }

    // 根据坐标获取城市
    async getCityByCoordinate(lat, lng) {
        return this.get('/store/cities', { lat, lng });
    }

    // 获取附近门店
    async getNearbyStores(lat, lng, showType = 2, beType = 1, orderType = 1) {
        return this.get('/store/stores/vicinity', {
            latitude: lat,
            longitude: lng,
            showType,
            beType,
            orderType
        });
    }

    // 获取门店菜单
    async getStoreMenu(storeCode, orderType = 1, beType = 1, beCode = '', dayPartCode = 5) {
        return this.get('/spc/menu', {
            storeCode,
            orderType,
            beType,
            beCode,
            dayPartCode
        });
    }

    // 获取商品详情
    async getProductDetail(storeCode, productCode, channelCode = '03', orderType = 1) {
        return this.get(`/spc/products/detail/${productCode}`, {
            storeCode,
            channelCode,
            orderType
        });
    }

    // 获取用户信息
    async getUserInfo(sid) {
        return this.get('/user/portal/info', { debug_sid: sid });
    }

    // 获取用户优惠券
    async getUserCoupons(sid) {
        return this.get('/promotion/coupons/rightCards', { debug_sid: sid });
    }

    // 获取优惠券详情
    async getCouponDetail(sid, couponId, couponCode) {
        return this.get(`/promotion/coupons/${couponCode}`, {
            debug_sid: sid,
            couponId
        });
    }

    // 获取订单详情
    async getOrderDetail(sid, orderId) {
        return this.get(`/order/orders/${orderId}`, { debug_sid: sid });
    }

    // 获取钱包余额
    async getWalletBalance(sid) {
        return this.get('/user/wallet/balance', { debug_sid: sid });
    }

    /**
     * 健康检查
     */
    async healthCheck() {
        try {
            const response = await this.get('/health');
            return {
                status: 'healthy',
                response,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 切换API端点
     */
    switchEndpoint(useLocalProxy) {
        const newBaseURL = useLocalProxy 
            ? config.get('mcdApi.localProxyUrl')
            : config.get('mcdApi.externalApiUrl');
        
        this.client.defaults.baseURL = newBaseURL;
        this.baseURL = newBaseURL;
        console.log(`🔄 API端点已切换到: ${newBaseURL}`);
    }
}

// 创建全局API客户端实例
const apiClient = new ApiClient();

module.exports = apiClient;
